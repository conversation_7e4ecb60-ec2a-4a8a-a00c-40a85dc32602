
/* /web/static/lib/bootstrap/scss/_functions.scss */
 

/* /web/static/lib/bootstrap/scss/_mixins.scss */
 

/* /web/static/src/scss/mixins_forwardport.scss */
 

/* /web/static/src/scss/bs_mixins_overrides.scss */
 

/* /web/static/src/legacy/scss/utils.scss */
 

/* /web_enterprise/static/src/scss/primary_variables.scss */
 

/* /web/static/src/scss/primary_variables.scss */
 

/* /web_enterprise/static/src/core/notifications/notifications.variables.scss */
 

/* /web_enterprise/static/src/webclient/home_menu/home_menu.variables.scss */
 

/* /web_enterprise/static/src/webclient/navbar/navbar.variables.scss */
 

/* /web/static/src/core/notifications/notification.variables.scss */
 

/* /web/static/src/search/control_panel/control_panel.variables.scss */
 

/* /web/static/src/search/search_panel/search_panel.variables.scss */
 

/* /web/static/src/views/form/form.variables.scss */
 

/* /web/static/src/views/kanban/kanban.variables.scss */
 

/* /web/static/src/webclient/burger_menu/burger_menu.variables.scss */
 

/* /web/static/src/webclient/navbar/navbar.variables.scss */
 

/* /base/static/src/scss/onboarding.variables.scss */
 

/* /mail/static/src/scss/variables/primary_variables.scss */
 

/* /web_editor/static/src/scss/web_editor.variables.scss */
 

/* /web_editor/static/src/scss/wysiwyg.variables.scss */
 

/* /portal/static/src/scss/primary_variables.scss */
 

/* /documents/static/src/scss/documents.variables.scss */
 

/* /web_gantt/static/src/scss/web_gantt.variables.scss */
 

/* /hr_org_chart/static/src/scss/variables.scss */
 

/* /web_enterprise/static/src/scss/secondary_variables.scss */
 

/* /web/static/src/scss/secondary_variables.scss */
 

/* /web_editor/static/src/scss/secondary_variables.scss */
 

/* /web/static/src/scss/pre_variables.scss */
 

/* /web/static/lib/bootstrap/scss/_variables.scss */
 

/* /im_livechat/static/src/scss/im_livechat_bootstrap.scss */
 .text-muted{color: #6c757d;}.text-start{text-align: left;}.text-center{text-align: center;}.o_thread_window, .o_thread_window *{box-sizing: border-box;}.o_thread_window .o_thread_window_header{height: 35px;}.o_thread_window .o_thread_window_header .fa-close{text-decoration: none; font-weight: bold;}.o_thread_window .o_thread_window_header .fa-close:before{content: "\00d7"; font-size: initial;}.o_thread_window .o_thread_window_header > span{margin: auto 0;}.o_thread_window .o_email_chat_button:after{content: ' \27A4';}.o_livechat_chatbot_end{font-style: italic !important; text-align: center !important; border: 1px solid #dee2e6 !important;}.o_livechat_chatbot_stepAnswer{display: inline-block !important; border: 1px solid #dee2e6 !important; border-color: #017e84 !important; border-radius: 0.25rem !important; padding: 0.5rem !important; margin-right: 1rem !important; margin-bottom: 0.25rem !important; font-weight: 700 !important;}.o_livechat_chatbot_options li:not(.disabled):hover{background-color: #017e84 !important;}

/* /im_livechat/static/src/legacy/public_livechat.scss */
 .o_thread_window{direction: ltr; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-flow: column nowrap; flex-flow: column nowrap; position: fixed; width: 340px; max-width: 100%; height: 460px; max-height: 100%; font-size: 12px; background-color: #FAFAFA; border-radius: 6px 6px 0 0; z-index: 1056; box-shadow: -5px -5px 10px rgba(0, 0, 0, 0.18);}@media (max-width: 767.98px){.o_thread_window{width: 100%; height: 100% !important; box-shadow: none;}.o_thread_window.o_folded{display: none;}}@media print{.o_thread_window{display: none;}}.o_thread_window .o_thread_window_header{align-items: center; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; color: white; padding: 5px 10px; border-radius: 3px 3px 0 0; border-bottom: 1px solid #dee2e6; background-color: #714B67; padding: 8px; cursor: pointer;}@media (max-width: 767.98px){.o_thread_window .o_thread_window_header{align-items: center; height: 46px; padding: 0; border-radius: 0px;}.o_thread_window .o_thread_window_header .o_thread_window_title{font-size: 16px; margin-left: 10px;}.o_thread_window .o_thread_window_header .o_thread_window_close{padding: 14.5px; font-size: 17px; color: white;}}.o_thread_window .o_thread_window_header .o_thread_window_avatar{margin: -6px 6px -6px 0; position: relative;}.o_thread_window .o_thread_window_header .o_thread_window_avatar img{height: 25px; width: 25px; border-radius: 50%;}.o_thread_window .o_thread_window_header .o_thread_window_avatar span{bottom: -4px; right: -2px; position: absolute;}.o_thread_window .o_thread_window_header .o_thread_window_avatar span .fa-circle-o{display: none;}.o_thread_window .o_thread_window_header .o_thread_window_title{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; display: inline-block; max-width: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; vertical-align: top;}.o_thread_window .o_thread_window_header .o_thread_window_title .o_mail_thread_typing_icon{padding-left: 2px;}.o_thread_window .o_thread_window_header .o_thread_window_title .o_mail_thread_typing_icon .o_mail_thread_typing_icon_dot{background: #dee2e6;}.o_thread_window .o_thread_window_header .o_thread_window_buttons{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto;}.o_thread_window .o_thread_window_header .o_thread_window_buttons .o_thread_window_close{color: white; padding: 0px 3px; margin-left: 5px; opacity: 0.7;}.o_thread_window .o_thread_window_header .o_thread_window_buttons .o_thread_window_close:hover, .o_thread_window .o_thread_window_header .o_thread_window_buttons .o_thread_window_close:focus, .o_thread_window .o_thread_window_header .o_thread_window_buttons .o_thread_window_close.focus{opacity: 1;}.o_thread_window .o_mail_thread{flex: 1 1 100%; overflow: auto; -webkit-overflow-scrolling: touch;}.o_thread_window .o_mail_thread .o_thread_date_separator{margin: 0px 0px 15px 0px;}.o_thread_window .o_mail_thread .o_thread_date_separator .o_thread_date{background-color: #FAFAFA;}.o_thread_window .o_mail_thread .o_thread_message{padding: 4px 5px;}.o_thread_window .o_mail_thread .o_thread_message .o_thread_message_sidebar{margin-right: 5px;}@media (min-width: 768px){.o_thread_window .o_mail_thread .o_thread_message .o_attachment{width: 33.33333333%;}}.o_thread_window .o_thread_composer input{width: 100%;}.o_thread_window_dropdown{width: auto; height: 28px; color: white; background-color: #212529; cursor: pointer; box-shadow: none;}@media (max-width: 767.98px){.o_thread_window_dropdown{display: none;}}.o_thread_window_dropdown .o_thread_window_header{border-radius: 0;}.o_thread_window_dropdown .o_thread_window_dropdown_toggler{padding: 5px;}.o_thread_window_dropdown .o_thread_window_dropdown_toggler .o_total_unread_counter{position: absolute; top: -10px; left: auto; bottom: auto; right: 0; background-color: #017e84; padding: 0 2px; font-size: smaller;}.o_thread_window_dropdown.show .o_thread_window_dropdown_toggler .o_total_unread_counter{display: none;}.o_thread_window_dropdown > ul{max-width: 340px; padding: 0;}.o_thread_window_dropdown > ul > li.o_thread_window_header{font-size: 12px; padding: 3px 5px;}.o_thread_window_dropdown > ul > li.o_thread_window_header ~ li.o_thread_window_header{border-top: 1px solid white;}.o_thread_window_dropdown > ul > li.o_thread_window_header:hover{background-color: #52374b;}.o_ui_blocked .o_thread_window{z-index: 1101;}.o_mail_thread_loading{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; justify-content: center;}.o_mail_thread_loading_icon{margin-right: 5px;}.o_mail_thread .o_thread_show_more, .o_mail_activity .o_thread_show_more{text-align: center;}.o_mail_thread .o_mail_thread_content, .o_mail_activity .o_mail_thread_content{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; min-height: 100%;}.o_mail_thread .o_thread_bottom_free_space, .o_mail_activity .o_thread_bottom_free_space{height: 15px;}.o_mail_thread .o_thread_date_separator, .o_mail_activity .o_thread_date_separator{margin-top: 15px; margin-bottom: 30px; border-bottom: 1px solid #ced4da; text-align: center;}@media (max-width: 767.98px){.o_mail_thread .o_thread_date_separator, .o_mail_activity .o_thread_date_separator{margin-top: 0px; margin-bottom: 15px;}}.o_mail_thread .o_thread_date_separator .o_thread_date, .o_mail_activity .o_thread_date_separator .o_thread_date{position: relative; top: 10px; margin: 0 auto; padding: 0 10px; font-weight: bold; background: white;}.o_mail_thread .o_thread_new_messages_separator, .o_mail_activity .o_thread_new_messages_separator{margin-bottom: 15px; border-bottom: solid #9d6b90 1px; text-align: right;}.o_mail_thread .o_thread_new_messages_separator .o_thread_separator_label, .o_mail_activity .o_thread_new_messages_separator .o_thread_separator_label{position: relative; top: 8px; padding: 0 10px; background: white; color: #9d6b90; font-size: smaller;}.o_mail_thread .o_thread_message, .o_mail_activity .o_thread_message{display: -webkit-box; display: -webkit-flex; display: flex; padding: 4px 16px; margin-bottom: 0px;}.o_mail_thread .o_thread_message.o_mail_not_discussion, .o_mail_activity .o_thread_message.o_mail_not_discussion{background-color: rgba(222, 226, 230, 0.5); border-bottom: 1px solid #ced4da;}.o_mail_thread .o_thread_message .o_thread_message_sidebar, .o_mail_activity .o_thread_message .o_thread_message_sidebar{flex: 0 0 36px; margin-right: 10px; margin-top: 2px; text-align: center; font-size: smaller;}.o_mail_thread .o_thread_message .o_thread_message_sidebar .o_thread_message_sidebar_image, .o_mail_activity .o_thread_message .o_thread_message_sidebar .o_thread_message_sidebar_image{position: relative; height: 36px;}.o_mail_thread .o_thread_message .o_thread_message_sidebar .o_thread_message_sidebar_image .o_updatable_im_status, .o_mail_activity .o_thread_message .o_thread_message_sidebar .o_thread_message_sidebar_image .o_updatable_im_status{width: 36px;}.o_mail_thread .o_thread_message .o_thread_message_sidebar .o_thread_message_sidebar_image .o_mail_user_status, .o_mail_activity .o_thread_message .o_thread_message_sidebar .o_thread_message_sidebar_image .o_mail_user_status{position: absolute; bottom: 0; right: 0;}.o_mail_thread .o_thread_message .o_thread_message_sidebar .o_thread_message_sidebar_image .o_mail_user_status.fa-circle-o, .o_mail_activity .o_thread_message .o_thread_message_sidebar .o_thread_message_sidebar_image .o_mail_user_status.fa-circle-o{display: none;}@media (max-width: 767.98px){.o_mail_thread .o_thread_message .o_thread_message_sidebar, .o_mail_activity .o_thread_message .o_thread_message_sidebar{margin-top: 4px; font-size: x-small;}}.o_mail_thread .o_thread_message .o_thread_message_sidebar .o_thread_message_avatar, .o_mail_activity .o_thread_message .o_thread_message_sidebar .o_thread_message_avatar{width: 36px; height: 36px; object-fit: cover;}.o_mail_thread .o_thread_message .o_thread_message_sidebar .o_thread_message_side_date, .o_mail_activity .o_thread_message .o_thread_message_sidebar .o_thread_message_side_date{display: none; margin-left: -5px;}.o_mail_thread .o_thread_message .o_thread_message_sidebar .o_thread_message_star, .o_mail_activity .o_thread_message .o_thread_message_sidebar .o_thread_message_star{display: none; margin-right: -5px;}.o_mail_thread .o_thread_message .o_thread_message_sidebar .o_thread_message_side_date, .o_mail_activity .o_thread_message .o_thread_message_sidebar .o_thread_message_side_date{opacity: 0;}.o_mail_thread .o_thread_message .o_thread_icon, .o_mail_activity .o_thread_message .o_thread_icon{cursor: pointer; opacity: 0;}.o_mail_thread .o_thread_message .o_thread_icon.fa-star, .o_mail_activity .o_thread_message .o_thread_icon.fa-star{opacity: 0.6; color: gold;}.o_mail_thread .o_thread_message:hover .o_thread_message_side_date, .o_mail_thread .o_thread_message.o_thread_selected_message .o_thread_message_side_date, .o_mail_activity .o_thread_message:hover .o_thread_message_side_date, .o_mail_activity .o_thread_message.o_thread_selected_message .o_thread_message_side_date{display: inline-block; opacity: 0.6;}.o_mail_thread .o_thread_message:hover .o_thread_icon, .o_mail_thread .o_thread_message.o_thread_selected_message .o_thread_icon, .o_mail_activity .o_thread_message:hover .o_thread_icon, .o_mail_activity .o_thread_message.o_thread_selected_message .o_thread_icon{display: inline-block; opacity: 0.6;}.o_mail_thread .o_thread_message:hover .o_thread_icon:hover, .o_mail_thread .o_thread_message.o_thread_selected_message .o_thread_icon:hover, .o_mail_activity .o_thread_message:hover .o_thread_icon:hover, .o_mail_activity .o_thread_message.o_thread_selected_message .o_thread_icon:hover{opacity: 1;}.o_mail_thread .o_thread_message .o_mail_redirect, .o_mail_activity .o_thread_message .o_mail_redirect{cursor: pointer;}.o_mail_thread .o_thread_message .o_thread_message_core, .o_mail_activity .o_thread_message .o_thread_message_core{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; min-width: 0; max-width: 100%; word-wrap: break-word;}.o_mail_thread .o_thread_message .o_thread_message_core > pre, .o_mail_activity .o_thread_message .o_thread_message_core > pre{white-space: pre-wrap; word-break: break-word; text-align: justify;}.o_mail_thread .o_thread_message .o_thread_message_core .o_mail_note_title, .o_mail_activity .o_thread_message .o_thread_message_core .o_mail_note_title{margin-top: 9px;}.o_mail_thread .o_thread_message .o_thread_message_core .o_mail_subject, .o_mail_activity .o_thread_message .o_thread_message_core .o_mail_subject{font-style: italic;}.o_mail_thread .o_thread_message .o_thread_message_core .o_mail_notification, .o_mail_activity .o_thread_message .o_thread_message_core .o_mail_notification{font-style: italic; color: gray;}.o_mail_thread .o_thread_message .o_thread_message_core [summary~=o_mail_notification], .o_mail_activity .o_thread_message .o_thread_message_core [summary~=o_mail_notification]{display: none;}.o_mail_thread .o_thread_message .o_thread_message_core p, .o_mail_activity .o_thread_message .o_thread_message_core p{margin: 0 0 9px;}.o_mail_thread .o_thread_message .o_thread_message_core p:last-child, .o_mail_activity .o_thread_message .o_thread_message_core p:last-child{margin-bottom: 0;}.o_mail_thread .o_thread_message .o_thread_message_core a, .o_mail_activity .o_thread_message .o_thread_message_core a{display: inline-block; word-break: break-all;}.o_mail_thread .o_thread_message .o_thread_message_core :not(.o_image_box) > img, .o_mail_activity .o_thread_message .o_thread_message_core :not(.o_image_box) > img{max-width: 100%; height: auto;}.o_mail_thread .o_thread_message .o_thread_message_core .o_mail_body_long, .o_mail_activity .o_thread_message .o_thread_message_core .o_mail_body_long{display: none;}.o_mail_thread .o_thread_message .o_thread_message_core .o_mail_info, .o_mail_activity .o_thread_message .o_thread_message_core .o_mail_info{margin-bottom: 2px;}.o_mail_thread .o_thread_message .o_thread_message_core .o_thread_message_star, .o_mail_thread .o_thread_message .o_thread_message_core .o_thread_message_needaction, .o_mail_thread .o_thread_message .o_thread_message_core .o_thread_message_reply, .o_mail_thread .o_thread_message .o_thread_message_core .o_thread_message_notification, .o_mail_activity .o_thread_message .o_thread_message_core .o_thread_message_star, .o_mail_activity .o_thread_message .o_thread_message_core .o_thread_message_needaction, .o_mail_activity .o_thread_message .o_thread_message_core .o_thread_message_reply, .o_mail_activity .o_thread_message .o_thread_message_core .o_thread_message_notification{padding: 4px;}.o_mail_thread .o_thread_message .o_thread_message_core .o_thread_message_notification, .o_mail_activity .o_thread_message .o_thread_message_core .o_thread_message_notification{color: grey;}.o_mail_thread .o_thread_message .o_thread_message_core .o_thread_message_notification.o_thread_message_notification_error, .o_mail_activity .o_thread_message .o_thread_message_core .o_thread_message_notification.o_thread_message_notification_error{color: red; opacity: 1; cursor: pointer;}.o_mail_thread .o_thread_message .o_thread_message_core .o_attachments_list:last-child, .o_mail_thread .o_thread_message .o_thread_message_core .o_attachments_previews:last-child, .o_mail_activity .o_thread_message .o_thread_message_core .o_attachments_list:last-child, .o_mail_activity .o_thread_message .o_thread_message_core .o_attachments_previews:last-child{margin-bottom: 1.5rem;}.o_mail_thread .o_thread_message .o_thread_message_core .o_mail_read_more, .o_mail_activity .o_thread_message .o_thread_message_core .o_mail_read_more{display: block;}.o_web_client.o_touch_device .o_mail_thread .o_thread_icon{opacity: 0.6;}.o_mail_thread_typing_icon{position: relative; text-align: center; margin-left: auto; margin-right: auto;}.o_mail_thread_typing_icon .o_mail_thread_typing_icon_dot{display: inline-block; width: 3px; height: 3px; border-radius: 50%; background: #343a40; animation: o_mail_thread_typing_icon_dot 1.5s linear infinite;}.o_mail_thread_typing_icon .o_mail_thread_typing_icon_dot:nth-child(2){animation-delay: -1.35s;}.o_mail_thread_typing_icon .o_mail_thread_typing_icon_dot:nth-child(3){animation-delay: -1.2s;}@keyframes o_mail_thread_typing_icon_dot{0%, 40%, 100%{transform: initial;}20%{transform: translateY(-5px);}}.o_livechat_button{position: fixed; right: 0; bottom: 0; margin-right: 12px; min-width: 100px; cursor: pointer; white-space: nowrap; background-color: rgba(60, 60, 60, 0.6); font-family: 'Lucida Grande', 'Lucida Sans Unicode', Arial, Verdana, sans-serif; font-size: 14px; font-weight: bold; padding: 10px; color: white; text-shadow: #3b4c58 1px 1px 0px; border: 1px solid #505050; border-bottom: 0px; border-top-left-radius: 5px; border-top-right-radius: 5px; z-index: 5;}.o_thread_window{z-index: 1046;}.o_thread_window .o_thread_date_separator{display: none;}.o_thread_window .btn{color: #FFFFFF; background-color: #30908e; border-color: #2d8685; border: 1px solid transparent;}.o_thread_window .btn-sm{padding: 0.0625rem 0.3125rem; font-size: 0.75rem; line-height: 1.5; border-radius: 0.2rem;}.o_thread_window .o_livechat_rating{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; overflow: auto; padding: 15px; font-size: 14px;}.o_thread_window .o_livechat_rating .o_livechat_email{font-size: 12px;}.o_thread_window .o_livechat_rating .o_livechat_email > div{display: -webkit-box; display: -webkit-flex; display: flex; padding: 5px 0;}.o_thread_window .o_livechat_rating .o_livechat_email > div input{display: block; width: 100%; height: calc(1.5em + 0.75rem + 2px); padding: 0.375rem 0.75rem; font-size: 0.875rem; font-weight: 400; line-height: 1.5; color: #495057; background-color: #FFFFFF; background-clip: padding-box; border: 1px solid #CED4DA;}.o_thread_window .o_livechat_rating .o_livechat_email > div button{display: inline-block; font-weight: 400; text-align: center; vertical-align: middle; user-select: none; padding: 0.375rem 0.75rem; font-size: 0.875rem; line-height: 1.5;}.o_thread_window .o_livechat_rating .o_livechat_no_feedback{text-decoration: underline; cursor: pointer; margin: 20px 0;}.o_thread_window .o_livechat_rating .o_livechat_rating_box{margin: 40px 0 30px 0;}.o_thread_window .o_livechat_rating .o_livechat_rating_choices{margin: 10px 0;}.o_thread_window .o_livechat_rating .o_livechat_rating_choices > img{width: 65px; opacity: 0.60; cursor: pointer; margin: 10px;}.o_thread_window .o_livechat_rating .o_livechat_rating_choices > img:hover, .o_thread_window .o_livechat_rating .o_livechat_rating_choices > img.selected{opacity: 1;}.o_thread_window .o_livechat_rating .o_livechat_rating_reason{margin: 10px 0px 25px 0px; display: none;}.o_thread_window .o_livechat_rating .o_livechat_rating_reason > textarea{width: 100%; height: 70px; resize: none;}.o_thread_window .o_livechat_rating .o_livechat_rating_reason_button > input{float: right;}.o_thread_window .o_composer_text_field{line-height: 1.3em;}.o_livechat_operator_avatar{padding-right: 8px;}.o_livechat_no_rating{opacity: 0.5;}.o_PublicLivechatMessage{display: -webkit-box; display: -webkit-flex; display: flex; position: relative;}.o_PublicLivechatMessage.o-isVisitorTheAuthor{flex-direction: row-reverse !important;}.o_PublicLivechatMessage_content{word-break: break-word; word-wrap: break-word;}.o_PublicLivechatMessage_header{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center;}.o_PublicLivechatMessage_header.o-isVisitorTheAuthor{-webkit-box-pack: end !important; justify-content: flex-end !important;}.o_PublicLivechatMessage_headerAuthor{margin-right: 0.5rem;}.o_PublicLivechatMessage_headerDate{opacity: 50%;}.o_PublicLivechatMessage_headerDatePrefix{opacity: 50%; margin-right: 0.125rem;}.o_PublicLivechatMessage_bubbleWrap{display: -webkit-box; display: -webkit-flex; display: flex; position: relative; align-items: flex-start;}.o_PublicLivechatMessage_bubbleWrap.o-isVisitorTheAuthor{-webkit-box-pack: end; justify-content: flex-end; margin-left: 1.5rem;}.o_PublicLivechatMessage_bubbleWrap.o-isOperatorTheAuthor{margin-right: 1.5rem;}.o_PublicLivechatMessage_bubble{position: relative;}.o_PublicLivechatMessage_bubble.o-isContentNonEmpty{padding: 0.75rem;}.o_PublicLivechatMessage_bubble.o-isVisitorTheAuthor{margin-left: 0.5rem;}.o_PublicLivechatMessage_bubble.o-isOperatorTheAuthor{margin-right: 0.5rem;}.o_PublicLivechatMessage_background{position: absolute; z-index: -1; left: 0; top: 0; width: 100%; height: 100%; transition: opacity .5s ease-out; margin-right: 3rem; border-bottom-right-radius: 0.6rem !important; border-bottom-left-radius: 0.6rem !important; border: 1px solid #C9CCD2 !important;}.o_PublicLivechatMessage_background.o-isVisitorTheAuthor{border-bottom-left-radius: 0.6rem !important; border-top-left-radius: 0.6rem !important; opacity: 50%; border-color: #198754 !important; background-color: rgba(40, 167, 69, 0.5) !important; color: #000; opacity: 25%;}.o_PublicLivechatMessage_background.o-isOperatorTheAuthor{border-top-right-radius: 0.6rem !important; border-bottom-right-radius: 0.6rem !important; border-color: #17a2b8 !important; background-color: rgba(23, 162, 184, 0.5) !important; color: #000; opacity: 15%;}.o_PublicLivechatWindow_composer{padding: 0.5rem; outline: none; border: 0; border-top: 1px solid lightgray;}

/* /im_livechat/static/src/legacy/public_livechat_chatbot.scss */
 .o_thread_window .o_livechat_chatbot_main_restart{color: white; opacity: 0.7;}.o_thread_window .o_livechat_chatbot_main_restart:hover, .o_thread_window .o_livechat_chatbot_main_restart:focus, .o_thread_window .o_livechat_chatbot_main_restart.focus{opacity: 1;}@media (max-width: 767.98px){.o_thread_window .o_livechat_chatbot_main_restart{padding: 14.5px; font-size: 17px; opacity: 1;}}.o_thread_window .o_livechat_chatbot_options{list-style: none; margin-block-start: 0px; margin-block-end: 0px; padding-inline-start: 0px;}.o_thread_window .o_livechat_chatbot_options li{user-select: none;}.o_thread_window .o_livechat_chatbot_options li:not(.disabled){cursor: pointer;}.o_thread_window .o_livechat_chatbot_options li:not(.disabled):hover{color: white; background-color: #0d6efd;}

/* /web/static/src/core/utils/transitions.scss */
 

/* /web/static/src/core/action_swiper/action_swiper.scss */
 .o_actionswiper{position: relative; touch-action: pan-y;}.o_actionswiper_target_container{transition: transform 0.4s;}.o_actionswiper_swiping{transition: none;}.o_actionswiper_right_swipe_area{transform: translateX(-100%); inset: 0 auto auto 0;}.o_actionswiper_left_swipe_area{transform: translateX(100%); inset: 0 0 auto auto;}

/* /web/static/src/core/autocomplete/autocomplete.scss */
 .o-autocomplete .o-autocomplete--input{width: 100%;}

/* /web/static/src/core/colorlist/colorlist.scss */
 .o_colorlist button{border: 1px solid #fff; box-shadow: 0 0 0 1px #adb5bd; width: 22px; height: 17px;}.o_colorlist .o_colorlist_selected{box-shadow: 0 0 0 2px #714B67 !important;}.o_colorlist_item_color_1{background-color: #F06050;}.o_colorlist_item_color_2{background-color: #F4A460;}.o_colorlist_item_color_3{background-color: #F7CD1F;}.o_colorlist_item_color_4{background-color: #6CC1ED;}.o_colorlist_item_color_5{background-color: #814968;}.o_colorlist_item_color_6{background-color: #EB7E7F;}.o_colorlist_item_color_7{background-color: #2C8397;}.o_colorlist_item_color_8{background-color: #475577;}.o_colorlist_item_color_9{background-color: #D6145F;}.o_colorlist_item_color_10{background-color: #30C381;}.o_colorlist_item_color_11{background-color: #9365B8;}.o_colorlist_item_color_0{position: relative;}.o_colorlist_item_color_0::before{content: ""; position: absolute; top: -2px; left: 10px; bottom: auto; right: auto; display: block; width: 1px; height: 19px; transform: rotate(45deg); background-color: red;}.o_colorlist_item_color_0::after{background-color: white;}

/* /web/static/src/core/commands/command_palette.scss */
 .o_command_palette > .modal-body{padding: 0;}.o_command_palette_listbox{max-height: 50vh;}.o_command_palette_listbox .o_command.focused{background: rgba(1, 126, 132, 0.25);}.o_command_palette_listbox .o_command_hotkey{align-items: center; justify-content: space-between; background-color: inherit; padding: 0.5rem 1.3em; display: -webkit-box; display: -webkit-flex; display: flex;}.o_command_palette_listbox .o_command_hotkey > icon{position: relative; top: 0.4em;}.o_command_palette_listbox .o_command a{text-decoration: none; color: inherit;}.o_command_palette .o_favorite{color: #f3cc00;}.o_command_palette .o_app_icon{height: 1.8rem; width: 1.8rem;}.o_command_palette .o_command{cursor: pointer;}.o_command_palette .o_command .text-ellipsis{text-overflow: ellipsis; white-space: nowrap; overflow: hidden;}.o_command_palette .o_command .o_command_focus{white-space: nowrap; opacity: 0.9;}

/* /web/static/src/core/dialog/dialog.scss */
 .modal.o_technical_modal .modal-footer footer, .modal.o_technical_modal .modal-footer .o_form_buttons_edit, .modal.o_technical_modal .modal-footer .o_form_buttons_view{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; -webkit-box-pack: start; justify-content: flex-start; gap: 0.25rem;}@media (max-width: 767.98px){.modal.o_technical_modal .modal-footer footer, .modal.o_technical_modal .modal-footer .o_form_buttons_edit, .modal.o_technical_modal .modal-footer .o_form_buttons_view{justify-content: space-around;}}.modal.o_technical_modal .modal-footer button{margin: 0;}

/* /web/static/src/core/dropdown/dropdown.scss */
 .o-dropdown{}.o-dropdown--menu{position: fixed;}.o-dropdown--menu .dropdown-toggle:focus, .o-dropdown--menu .dropdown-item:focus{background-color: transparent; outline: none;}.o-dropdown--menu .dropdown-toggle.focus, .o-dropdown--menu .dropdown-item.focus{background-color: #e9ecef;}.o-dropdown--menu .dropdown-item:not(.disabled):not(:disabled), .o-dropdown--menu .dropdown-item:not(.disabled):not(:disabled) label{cursor: pointer;}.o-dropdown.dropup > .o-dropdown--menu, .o-dropdown.dropdown > .o-dropdown--menu, .o-dropdown.dropstart > .o-dropdown--menu, .o-dropdown.dropend > .o-dropdown--menu{left: auto; right: auto; margin-left: 0; margin-right: 0;}.o-dropdown--no-caret > .dropdown-toggle::before, .o-dropdown--no-caret > .dropdown-toggle::after{content: normal;}.o-dropdown button.dropdown-toggle.active, .o-dropdown button.dropdown-toggle:hover, .o-dropdown button.dropdown-toggle:focus, .o-dropdown button.dropdown-toggle:active{outline: none; box-shadow: none !important;}.o-dropdown button.dropdown-toggle.dropdown-item:not(.o_menu_item)::after, .o-dropdown button.dropdown-toggle.dropdown-item:not(.o_menu_item)::before{position: absolute; top: 0; left: auto; bottom: auto; right: 0; transform: translate(-0.6em, 0.6em) ;}

/* /web/static/src/core/effects/rainbow_man.scss */
 .o_reward{will-change: transform; z-index: 1055; animation: reward-fading 0.7s ease-in-out forwards;}.o_reward .o_reward_box{transform-box: fill-box;}.o_reward.o_reward_fading{animation: reward-fading-reverse 0.56s ease-in-out forwards;}.o_reward.o_reward_fading .o_reward_face_group{animation: reward-jump-reverse 0.56s ease-in-out forwards;}.o_reward.o_reward_fading .o_reward_rainbow_line{animation: reward-rainbow-reverse 0.7s ease-out forwards;}.o_reward .o_reward_rainbow_man{max-width: 400px;}.o_reward .o_reward_rainbow_line{animation: reward-rainbow 1.12s ease-out 1 forwards;}.o_reward .o_reward_face_group{animation: reward-jump 1.12s ease-in-out 1;}.o_reward .o_reward_face_wrap{animation: reward-rotate 1.12s cubic-bezier(0.51, 0.92, 0.24, 1.15) 1;}.o_reward .o_reward_face{animation: reward-float 1.4s ease-in-out 1.4s infinite alternate;}.o_reward .o_reward_star_01, .o_reward .o_reward_star_03{animation: reward-stars 1.4s ease-in-out infinite alternate-reverse;}.o_reward .o_reward_star_02, .o_reward .o_reward_star_04{animation: reward-stars 1.68s ease-in-out infinite alternate;}.o_reward .o_reward_thumbup{animation: reward-scale 0.7s ease-in-out 0s infinite alternate;}.o_reward .o_reward_shadow_container{animation: reward-float 1.4s ease-in-out infinite alternate;}.o_reward .o_reward_shadow{animation: reward-scale 1.4s ease-in-out infinite alternate;}.o_reward .o_reward_msg_container{aspect-ratio: 1 / 1; animation: reward-float-reverse 1.4s ease-in-out infinite alternate-reverse;}@keyframes reward-fading{0%{opacity: 0;}}@keyframes reward-fading-reverse{100%{opacity: 0;}}@keyframes reward-jump{0%{transform: scale(0.5);}50%{transform: scale(1.05);}}@keyframes reward-jump-reverse{50%{transform: scale(1.05);}to{transform: scale(0.5);}}@keyframes reward-rainbow{to{stroke-dashoffset: 0;}}@keyframes reward-rainbow-reverse{from{stroke-dashoffset: 0;}}@keyframes reward-float{to{transform: translateY(5px);}}@keyframes reward-float-reverse{from{transform: translateY(5px);}}@keyframes reward-stars{from{transform: scale(0.3) rotate(0deg);}50%{transform: scale(1) rotate(20deg);}to{transform: scale(0.3) rotate(80deg);}}@keyframes reward-scale{from{transform: scale(0.8);}}@keyframes reward-rotate{from{transform: scale(0.5) rotate(-30deg);}}

/* /web/static/src/core/file_upload/file_upload_progress_bar.scss */
 .o-file-upload-progress-bar-value{transition: width 0.1s; border-right: 1px solid #015a5e; background-color: #017e84; opacity: 0.5;}.o-file-upload-progress-bar-abort{padding: 4px; color: #963535; font-size: 16px;}.o-file-upload-progress-bar-abort:active{opacity: 0.7;}div:not(:hover) .o-file-upload-progress-bar-abort{display: none;}

/* /web/static/src/core/file_upload/file_upload_progress_record.scss */
 .o_kanban_record .o_kanban_progress_card{min-height: 80px;}.o_kanban_record .o_kanban_progress_card .o_kanban_record_bottom{color: #212529;}.o_kanban_record .o_kanban_progress_card .o_kanban_image_wrapper{opacity: 0.7;}.o_data_row.o_list_progress_card{height: 25px; border: 1px solid #dfdfdf;}.o_data_row.o_list_progress_card .o_file_upload_upload_title{font-size: 13px; font-weight: 500;}

/* /web/static/src/core/model_field_selector/model_field_selector.scss */
 .o_field_selector:not(.o_legacy_field_selector){position: relative;}.o_field_selector:not(.o_legacy_field_selector) > .o_field_selector_value{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-flow: row wrap; flex-flow: row wrap; align-items: center; height: 100%; min-height: 20px;}.o_field_selector:not(.o_legacy_field_selector) > .o_field_selector_value:active, .o_field_selector:not(.o_legacy_field_selector) > .o_field_selector_value:focus, .o_field_selector:not(.o_legacy_field_selector) > .o_field_selector_value:active:focus{outline: none;}.o_field_selector:not(.o_legacy_field_selector) > .o_field_selector_value > .o_field_selector_chain_part{padding: 0px 1px; border: 1px solid #d5dae8; background: #f6f7fa; margin-bottom: 1px;}.o_field_selector:not(.o_legacy_field_selector) > .o_field_selector_value > i{align-self: center; margin: 0 2px; font-size: 10px;}.o_field_selector:not(.o_legacy_field_selector) > .o_field_selector_controls{position: absolute; top: 0; left: auto; bottom: 1px; right: 0; display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; cursor: pointer;}.o_field_selector:not(.o_legacy_field_selector).o_edit_mode > .o_field_selector_controls::after{content: ""; display: inline-block; width: 0; height: 0; vertical-align: middle; -moz-transform: scale(0.9999); border-bottom: 0; border-left: 0.3em solid transparent; border-right: 0.3em solid transparent; border-top: 0.3em solid var(--o-caret-color, currentColor);}.o_field_selector:not(.o_legacy_field_selector).o_edit_mode > .o_field_selector_popover{position: absolute; top: 100%; left: 0; bottom: auto; right: auto;}

/* /web/static/src/core/model_field_selector/model_field_selector_popover.scss */
 .o_popover_field_selector .popover-arrow::after{border-bottom-color: #0d6efd;}.o_field_selector_popover:not(.o_legacy_field_selector_popover){width: 265px; background: white; --o-input-background-color: white;}.o_field_selector_popover:not(.o_legacy_field_selector_popover):focus{outline: none;}.o_field_selector_popover:not(.o_legacy_field_selector_popover) .o_field_selector_popover_header{color: white; background: #0d6efd; font-weight: bold; padding: 5px 0 5px 0.4em;}.o_field_selector_popover:not(.o_legacy_field_selector_popover) .o_field_selector_popover_header .o_field_selector_title{width: 100%; display: inline-block; max-width: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; vertical-align: top; padding: 0px 35px; text-align: center;}.o_field_selector_popover:not(.o_legacy_field_selector_popover) .o_field_selector_popover_header .o_field_selector_search{padding-right: 0.4rem;}.o_field_selector_popover:not(.o_legacy_field_selector_popover) .o_field_selector_popover_header .o_field_selector_search > .o_input{font-size: 13px; padding: 5px 0.4rem; text-align: left; line-height: normal;}.o_field_selector_popover:not(.o_legacy_field_selector_popover) .o_field_selector_popover_header .o_field_selector_popover_option{position: absolute; top: 0; left: auto; bottom: auto; right: auto; padding: 8px;}.o_field_selector_popover:not(.o_legacy_field_selector_popover) .o_field_selector_popover_header .o_field_selector_popover_option.o_field_selector_prev_page{left: 0;}.o_field_selector_popover:not(.o_legacy_field_selector_popover) .o_field_selector_popover_header .o_field_selector_popover_option.o_field_selector_close{right: 0;}.o_field_selector_popover:not(.o_legacy_field_selector_popover) .o_field_selector_popover_header .o_field_selector_popover_option:hover{background: #0257d5;}.o_field_selector_popover:not(.o_legacy_field_selector_popover) .o_field_selector_popover_body .o_field_selector_page{position: relative; height: 320px; overflow: auto; margin: 0; padding: 0;}.o_field_selector_popover:not(.o_legacy_field_selector_popover) .o_field_selector_popover_body .o_field_selector_page > .o_field_selector_item{list-style: none; position: relative; padding: 5px 0 5px 0.4em; cursor: pointer; font-family: Arial; font-size: 13px; color: #444; border-bottom: 1px solid #eee;}.o_field_selector_popover:not(.o_legacy_field_selector_popover) .o_field_selector_popover_body .o_field_selector_page > .o_field_selector_item.active{background: #f5f5f5;}.o_field_selector_popover:not(.o_legacy_field_selector_popover) .o_field_selector_popover_body .o_field_selector_page > .o_field_selector_item .o_field_selector_item_title{font-size: 12px;}.o_field_selector_popover:not(.o_legacy_field_selector_popover) .o_field_selector_popover_body .o_field_selector_page > .o_field_selector_item .o_field_selector_relation_icon{position: absolute; top: 0; left: auto; bottom: 0; right: 0; display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; padding: 10px;}.o_field_selector_popover:not(.o_legacy_field_selector_popover) .o_field_selector_popover_footer{background: #0d6efd; padding: 5px 0.4em;}.o_field_selector_popover:not(.o_legacy_field_selector_popover) .o_field_selector_popover_footer > input{font-size: 13px; width: 100%; padding: 0 0.4rem;}

/* /web/static/src/core/notebook/notebook.scss */
 .o_notebook{--notebook-margin-x: 0; --notebook-padding-x: 0; --notebook-link-border-color: transparent; --notebook-link-border-color-active: #dee2e6; --notebook-link-border-color-hover: #e9ecef; --notebook-link-border-color-active-accent: #dee2e6;}.o_notebook .o_notebook_headers{margin: 0 var(--notebook-margin-x, 0);}@media (max-width: 767.98px){.o_notebook .o_notebook_headers{overflow-x: auto;}.o_notebook .o_notebook_headers::-webkit-scrollbar{display: none;}}.o_notebook .nav{padding: 0 var(--notebook-padding-x, 0); background-color: white;}.o_notebook .nav-item{white-space: nowrap; margin: 0 -1px 0 0;}.o_notebook .nav-link{border-color: var(--notebook-link-border-color, transparent);}.o_notebook .nav-link.active, .o_notebook .nav-link.active:hover, .o_notebook .nav-link.active:focus, .o_notebook .nav-link.active:active{border-color: var(--notebook-link-border-color-active); border-top-color: var(--notebook-link-border-color-active-accent, var(--notebook-link-border-color-active)); border-bottom-color: white;}.o_notebook .nav-link:hover, .o_notebook .nav-link:focus, .o_notebook .nav-link:active{outline: none;}.o_notebook .nav-link:hover{border-color: var(--notebook-link-border-color-hover);}.o_notebook.vertical .nav{width: max-content; border-bottom-color: transparent;}.o_notebook.vertical .nav-item{margin: 0 0 -1px 0;}.o_notebook.vertical .nav-item:first-child .nav-link{border-top-width: 0;}.o_notebook.vertical .nav-item:last-child .nav-link{border-bottom-width: 0;}.o_notebook.vertical .nav-link{margin-bottom: 0;}.o_notebook.vertical .nav-link.active, .o_notebook.vertical .nav-link.active:hover, .o_notebook.vertical .nav-link.active:focus, .o_notebook.vertical .nav-link.active:active{border-color: var(--notebook-link-border-color-active); border-left-color: var(--notebook-link-border-color-active-accent, var(--notebook-link-border-color-active)); border-right-color: white;}

/* /web/static/src/core/notifications/notification.scss */
 .o_notification_manager{position: absolute; top: 46px; left: calc(100vw - 320px); bottom: auto; right: 0.5rem; z-index: 1055;}.o_notification_manager .o_notification{border-left-width: 0.75rem !important; box-shadow: 0 12px 14px -10px rgba(0, 0, 0, 0.25);}.o_notification_manager .o_notification_close{position: absolute; top: 0; left: auto; bottom: auto; right: 0;}.o_notification_fade{transition: all 0.5s;}.o_notification_fade-enter{opacity: 0;}

/* /web/static/src/core/popover/popover.scss */
 @keyframes slide-top{0%{opacity: 0; transform: translateY(-5%);}}@keyframes slide-end{0%{opacity: 0; transform: translateX(5%);}}@keyframes slide-bottom{0%{opacity: 0; transform: translateY(5%);}}@keyframes slide-start{0%{opacity: 0; transform: translateX(-5%);}}.o_popover.bs-popover-top{animation: 0.2s slide-top;}.o_popover.bs-popover-end{animation: 0.2s slide-end;}.o_popover.bs-popover-bottom{animation: 0.2s slide-bottom;}.o_popover.bs-popover-start{animation: 0.2s slide-start;}.o_popover.o-popover-top, .o_popover.o-popover-auto[x-placement^="top"]{margin-bottom: 0.5rem;}.o_popover.o-popover-right, .o_popover.o-popover-auto[x-placement^="right"]{margin-left: 0.5rem;}.o_popover.o-popover-bottom, .o_popover.o-popover-auto[x-placement^="bottom"]{margin-top: 0.5rem;}.o_popover.o-popover-left, .o_popover.o-popover-auto[x-placement^="left"]{margin-right: 0.5rem;}.o_popover.o-popover--ts .popover-arrow, .o_popover.o-popover--bs .popover-arrow{left: 0.3rem;}.o_popover.o-popover--te .popover-arrow, .o_popover.o-popover--be .popover-arrow{right: 0.3rem;}.o_popover.o-popover--ls .popover-arrow, .o_popover.o-popover--rs .popover-arrow{top: 0.3rem;}.o_popover.o-popover--le .popover-arrow, .o_popover.o-popover--re .popover-arrow{bottom: 0.3rem;}.o_popover.o-popover-no-arrow > .popover-arrow{display: none;}.o_popover.o-popover-no-arrow{border: 0px;}

/* /web/static/src/core/tooltip/tooltip.scss */
 .o-tooltip{font-size: small; max-width: 400px;}.o-tooltip .o-tooltip--string{background-color: white; font-weight: bold; padding: 5px 8px;}.o-tooltip .o-tooltip--help{font-size: smaller; white-space: pre-line; padding: 8px; margin-bottom: 0px;}.o-tooltip .o-tooltip--technical{font-size: smaller; padding: 8px; margin: 0 0 0 15px; list-style-type: circle;}.o-tooltip .o-tooltip--technical .o-tooltip--technical--title{font-weight: bold; margin-right: 4px;}

/* /web/static/src/core/ui/block_ui.scss */
 .o_blockUI{cursor: wait; -webkit-backdrop-filter: blur(2px); backdrop-filter: blur(2px); z-index: 1070 !important;}