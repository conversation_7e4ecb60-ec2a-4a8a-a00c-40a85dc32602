from typing import Annotated

from odoo.exceptions import UserError
from ..schemas import CrmLeadDatas,MailMessageDatas, CareHistoryResponse, duplicateLeadNotifyDatas, ResPartnerDatas, RecordDatas
from fastapi import APIRouter, Depends, HTTPException, Path
from ..dependencies import authenticated_fastapi_endpoint
from odoo.addons.fastapi.models.fastapi_endpoint import FastapiEndpoint as ThFastapi
import time
from odoo.fields import Datetime, Date

router = APIRouter(tags=["CRM"])


@router.post("/api/crmlead/{id_b2b}")
def call_crm_lead(
    crm_lead_data: CrmLeadDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id_b2b: int = Path(..., ge=1)
):
    try:
        if fastapi:
            start = time.perf_counter()
            result = update_data_crm(crm_lead_data, fastapi, id_b2b)
            result['processing_time'] = round((time.perf_counter() - start) * 1000, 2)
            return result
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


def update_data_crm(
    crm_lead_data: CrmLeadDatas,
    fastapi,
    id_b2b: int = Path(..., ge=1)
):
    values_b2b = crm_lead_data.model_dump(exclude_none=True, exclude_unset=True)
    partner_b2b_id = values_b2b.get('partner_id', None)
    # partner_b2b_info = values_b2b.get('partner_info').copy() if values_b2b.get('partner_info') else None
    values = fastapi.env['crm.lead'].map_data_from_sync(values_b2b) or {} # val_samp
    partner_info = values.pop('partner_info', None) if values_b2b.get('partner_info') else None
    # Nếu id_b2b là None, tìm kiếm mapping theo th_internal_id từ dữ liệu gửi lên
    if id_b2b is None:
        # Đây là record mới chưa có external_id, cần tạo mới
        mapid = None
    else:
        mapid = fastapi.env['th.mapping.id'].search([
            ('th_external_id', '=', id_b2b),
            ('th_model_name', '=', 'crm.lead'),
            ('th_system', '=', 'b2b')
        ], limit=1)

    if mapid and mapid.th_internal_id:  # write
        fastapi.env['crm.lead'].browse(mapid.th_internal_id).sudo().with_context(th_sync=True).write(values)
    else:  # create
        partner_map = None
        data_partner = {}
        if not values.get('partner_id') and partner_info:
            partner = fastapi.env['res.partner'].search([('phone', '=', partner_info['phone'])], limit=1)
            if partner:
                values['partner_id'] = partner.id
                data_partner['street'] = partner.street
                data_partner['email'] = partner.email
                data_partner['th_module_ids'] = partner.th_module_ids.ids
                data_partner['th_citizen_identification'] = partner.th_citizen_identification
                data_partner['th_affiliate_code'] = partner.th_affiliate_code
                data_partner['th_customer_code'] = partner.th_customer_code
                data_partner['name'] = partner.name
                data_partner['th_phone2'] = partner.th_phone2
                data_partner['th_gender'] = partner.th_gender
                data_partner['th_birthday'] = partner.th_birthday
                data_partner['function'] = partner.function
                data_partner['th_date_identification'] = partner.th_date_identification
                data_partner['title'] = partner.title.id
                data_partner['th_place_identification'] = partner.th_place_identification
                data_partner['vat'] = partner.vat
                data_partner['lang'] = partner.lang
                data_partner['street'] = partner.street
                data_partner['th_district_id'] = partner.th_district_id.id
                data_partner['state_id'] = partner.state_id.id
                data_partner['country_id'] = partner.country_id.id
                partner_map = partner
            else:
                # chưa có partner
                partner = fastapi.env['res.partner'].create(partner_info)
                values['partner_id'] = partner.id
                partner_map = partner
        crm_lead = fastapi.env['crm.lead'].sudo().with_context(th_sync=True).create(values)
        if crm_lead:
            # Tạo mapping mới với external_id là ID của CRM lead vừa tạo
            # Nếu id_b2b là None, sử dụng crm_lead.id làm external_id
            external_id = id_b2b if id_b2b is not None else crm_lead.id
            fastapi.env['th.mapping.id'].create({
                'name': crm_lead.name,
                'th_model_name': 'crm.lead',
                'th_internal_id': crm_lead.id,
                'th_external_id': external_id,
                'th_system': 'b2b',
                'th_module_id': fastapi.env.ref('th_setup_parameters.th_crm_module').id,
            })
            if partner_map:
                # Tạo hoặc cập nhật mapping cho partner
                partner_mapping = fastapi.env['th.mapping.id'].search([
                    ('th_model_name', '=', 'res.partner'),
                    ('th_internal_id', '=', partner_map.id),
                    ('th_system', '=', 'b2b')
                ], limit=1)

                if partner_mapping:
                    # Cập nhật external_id nếu có
                    if partner_b2b_id:
                        partner_mapping.th_external_id = partner_b2b_id
                else:
                    # Tạo mapping mới
                    fastapi.env['th.mapping.id'].create({
                        'name': partner_map.name,
                        'th_model_name': 'res.partner',
                        'th_internal_id': partner_map.id,
                        'th_external_id': partner_b2b_id,
                        'th_system': 'b2b',
                    })

        result = {
            'id': external_id,  # Trả về external_id để cập nhật mapping
            'crm_lead': {
                'name': crm_lead.name,
                'th_is_a_duplicate_opportunity': crm_lead.th_is_a_duplicate_opportunity,
                'th_person_in_charge': crm_lead.user_id.name if crm_lead.user_id else False
            },
            'partner_id': data_partner
        }

        # Thêm partner_id vào response để xử lý mapping
        if partner_map:
            # Tìm external_id của partner từ mapping
            partner_mapping = fastapi.env['th.mapping.id'].search([
                ('th_model_name', '=', 'res.partner'),
                ('th_internal_id', '=', partner_map.id),
                ('th_system', '=', 'b2b')
            ], limit=1)

            if partner_mapping and partner_mapping.th_external_id:
                result['partner_id_external'] = partner_mapping.th_external_id

        return result


@router.post("/api/updatecrm/{id_b2b}")
async def sync_crm(
    records: list[RecordDatas],
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            results = []
            for record in records:
                try:
                    response = update_data_crm(record.th_data_crm, fastapi, record.id_b2b)
                    results.append({
                        "status": "success",
                        "response": response,
                    })
                except Exception as e:
                    results.append({
                        "status": "error",
                        "response": str(e),
                    })
            return results
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/crmlead/{id_b2b}")
def delete_crm_lead(
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id_b2b: int = Path(..., ge=0)
):
    try:
        if fastapi:
            # Tìm ID mapping tương ứng trên SamP
            mapping = fastapi.env['th.mapping.id'].search([
                ('th_external_id', '=', id_b2b),
                ('th_model_name', '=', 'crm.lead'),
                ('th_system', '=', 'b2b')
            ], limit=1)

            if not mapping or not mapping.th_internal_id:
                raise HTTPException(status_code=404, detail="Không tìm thấy bản ghi tương ứng trong SamP.")

            # Tìm bản ghi CRM Lead trên SamP
            crm_lead_samp = fastapi.env['crm.lead'].browse(mapping.th_internal_id)
            if not crm_lead_samp.exists():
                raise HTTPException(status_code=404, detail="Không tìm thấy cơ hội trong SamP.")

            # Kiểm tra điều kiện: chỉ cho phép xóa các cơ hội chưa bàn giao
            for lead in crm_lead_samp:
                if lead.partner_id and lead.state == 'transfer':
                    raise HTTPException(
                        status_code=403,
                        detail=f"Cơ hội đã bàn giao, không thể xóa cơ hội {lead.id}."
                    )

            # Xóa bản ghi CRM Lead và mapping ID trên SamP
            crm_lead_samp.sudo().with_context(th_sync=True).unlink()
            mapping.sudo().unlink()

            return {"status": "success", "message": "Xóa thành công cơ hội trên SamP.", "id_b2b": id_b2b}

    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Lỗi hệ thống: {str(e)}")


@router.post("/api/mailmessages/{id_b2b}")
async def create_mail_messages(
    records: list[RecordDatas],
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            results = []
            for record in records:
                try:
                    response = create_mail_message(record.th_data_mail, fastapi, record.id_b2b)
                    results.append({
                        "status": "success",
                        "response": response,
                    })
                except Exception as e:
                    results.append({
                        "status": "error",
                        "response": str(e),
                    })
            return results
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


def create_mail_message(
    MailMessageDatas: MailMessageDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id_b2b: int = Path(..., ge=1)
):
    try:
        if fastapi:
            values_b2b = MailMessageDatas.model_dump(exclude_none=True, exclude_unset=True) # val_samp
            mapid_crm = fastapi.env['th.mapping.id'].search([
                ('th_external_id', '=', values_b2b['res_id']),
                ('th_model_name', '=', values_b2b['model']),
                ('th_system', '=', 'b2b')
            ], limit=1)
            mapid = fastapi.env['th.mapping.id'].search([
                ('th_external_id', '=', id_b2b),
                ('th_model_name', '=', 'mail.message'),
                ('th_system', '=', 'b2b')
            ], limit=1)
            values_b2b['res_id']= mapid_crm.th_internal_id
            if mapid and mapid.th_internal_id:
                fastapi.env['mail.message'].browse(mapid.th_internal_id).sudo().with_context(th_sync=True).write(values_b2b)
            elif mapid_crm and mapid_crm.th_internal_id:
                mail_message = fastapi.env['mail.message'].sudo().with_context(th_sync=True).create(values_b2b)
                if mail_message:
                    fastapi.env['th.mapping.id'].create({
                        'name': mail_message.model,
                        'th_model_name': 'mail.message',
                        'th_internal_id': mail_message.id,
                        'th_external_id': id_b2b,
                        'th_system': 'b2b',

                    })
                result = {
                    'mail_message':{
                        'name': mail_message.model,
                    }
                }
                return result

    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/mailmessage/{id}")
def delete_apm_lead(
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id: int = Path(..., ge=0)
):
    try:
        if fastapi:

            mapid = fastapi.env['th.mapping.id'].search([
                ('th_external_id', '=', id),
                ('th_model_name', '=', 'mail.message'),
                ('th_system', '=', 'b2b')
            ], limit=1)
            if mapid:
                mail_message = fastapi.env['mail.message'].browse(mapid.th_internal_id)
            if not mail_message.exists():
                raise HTTPException(status_code=404, detail="Không tìm thấy message.")
            mail_message.sudo().with_context(th_sync=True).unlink()
            return

    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/api/carehistory/{id_crm_b2b}")
def call_crm_lead(
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id_crm_b2b: int = Path(..., ge=1)
):
    try:
        if fastapi:
            mapid = fastapi.env['th.mapping.id'].search([
                ('th_external_id', '=', id_crm_b2b),
                ('th_model_name', '=', 'crm.lead'),
                ('th_system', '=', 'b2b')
            ], limit=1)

            if mapid and mapid.th_internal_id:  # write
                crm_lead = fastapi.env['crm.lead'].browse(mapid.th_internal_id)
                crm_id = crm_lead.sudo().search(
                    [('partner_id', '=', crm_lead.partner_id.id), ('th_origin_id', '=', crm_lead.th_origin_id.id),
                     ('id', '!=', crm_lead.id), ('th_is_a_duplicate_opportunity', '=', False),
                     ('type', '=', 'opportunity')], order='id asc')
                if len(crm_id) > 1:
                    crm_id = crm_lead.env['crm.lead'].sudo().search(
                        [('id', '!=', crm_lead.id), ('th_origin_id', '=', crm_lead.th_origin_id.id),
                         ('partner_id', '=', crm_lead.partner_id.id), ('th_is_a_duplicate_opportunity', '=', False),
                         ('th_is_close_lead', '=', False)])
                    if not crm_id:
                        crm_id = crm_lead.env['crm.lead'].sudo().search(
                            [('id', '!=', crm_lead.id), ('th_origin_id', '=', crm_lead.th_origin_id.id),
                             ('partner_id', '=', crm_lead.partner_id.id), ('th_is_a_duplicate_opportunity', '=', False)],
                            limit=1)
                result = []
                for crm in crm_id:
                    care_history = crm_lead.env['th.care.history'].search(
                            [('th_crm_lead_old_id', '=', crm.id), ('th_crm_lead_new_id', '=', crm_lead.id)])
                    if not care_history:
                        care_history = crm_lead.env['th.care.history'].create(
                            {'th_crm_lead_old_id': crm.id, 'th_crm_lead_new_id': crm_lead.id})
                    result.append(CareHistoryResponse(name=care_history.name, preview=care_history.preview))
                return result
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/api/duplicateleadnotify/{id_b2b}")
def create_duplicate_lead_notify(
    duplicateLeadNotifyDatas: list[RecordDatas],
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
):
    try:
        if fastapi:
            results = []
            for record in duplicateLeadNotifyDatas:
                try:
                    values_b2b = record.th_data_dup_CRM.model_dump(exclude_none=True, exclude_unset=True) # val_samp
                    mapid_crm = fastapi.env['th.mapping.id'].search([
                        ('th_external_id', '=', record.id_b2b),
                        ('th_model_name', '=', 'crm.lead'),
                        ('th_system', '=', 'b2b')
                    ], limit=1)
                    if mapid_crm:
                        vals = {
                            'new_lead_id': mapid_crm.th_internal_id,
                            'th_description': values_b2b.get('th_description', False)
                        }
                        duplicate_lead_notify = fastapi.env['th.duplicate.lead.notify.wr'].sudo().create(vals)
                        response = duplicate_lead_notify.th_send_noti()
                        results.append({
                            "status": "success",
                            "response": response,
                        })
                except Exception as e:
                        results.append({
                            "status": "error",
                            "response": str(e),
                        })
            return results

    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/api/mappingidcrmstage/")
def mapping_id_crm_stage(
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    ids_b2b: list[int]
):
    try:
        if fastapi:
            crm_stage_exist = fastapi.env['th.mapping.id'].sudo().search([('th_external_id', 'in', ids_b2b), ('th_model_name', '=', 'crm.stage'), ('th_system', '=', 'b2b')])
            if len(crm_stage_exist) == len(ids_b2b):
                return False
            crm_stage_exist.unlink()
            for i, externalID in enumerate(ids_b2b):
                crm_stage = fastapi.env.ref(f'th_crm.th_stage_lead{i + 1}')
                fastapi.env['th.mapping.id'].sudo().create({
                    'name': crm_stage.name,
                    'th_internal_id': crm_stage.id,
                    'th_external_id': externalID,
                    'th_model_name': 'crm.stage',
                    'th_system': 'b2b'
                })
            return True
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/api/contact/{id_b2b}")
def call_contact(
    contact_data: ResPartnerDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id_b2b: int = Path(..., ge=1)
):
    try:
        if fastapi:
            return update_contact(contact_data, fastapi, id_b2b)
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


def update_contact(
    contact_data: ResPartnerDatas,
    fastapi,
    id_b2b: int = Path(..., ge=1)
):
    values_b2b = contact_data.model_dump(exclude_none=True, exclude_unset=True)  # val_samp
    mapid = fastapi.env['th.mapping.id'].search([
        ('th_external_id', '=', id_b2b),
        ('th_model_name', '=', 'res.partner'),
        ('th_system', '=', 'b2b')
    ], limit=1)

    if not mapid or not mapid.th_internal_id:
        raise HTTPException(status_code=404, detail="Không tìm thấy liên hệ trong hệ thống SAMP.")

    partner = fastapi.env['res.partner'].browse(mapid.th_internal_id)
    if not partner.exists():
        raise HTTPException(status_code=404, detail="Liên hệ không tồn tại trong hệ thống SAMP.")

    if contact_data.country_id:
        country = fastapi.env["res.country"].sudo().search(
            [("code", "=", contact_data.country_id)], limit=1
        )
        if country:
            values_b2b['country_id'] = country.id

    if contact_data.th_country_id:
        th_country = fastapi.env["res.country"].sudo().search(
            [("code", "=", contact_data.th_country_id)], limit=1
        )
        if th_country:
            values_b2b['th_country_id'] = th_country.id

    partner.sudo().with_context(th_sync=True).write(values_b2b)

    return {"message": "Liên hệ đã được cập nhật thành công.", "contact_id": partner.id}

@router.post("/api/formio/crmleads")
def create_crm_lead(
    lead_data: CrmLeadDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    start = time.perf_counter()
    try:
        if not fastapi:
            raise HTTPException(status_code=401, detail="Không có quyền truy cập")

        # Khởi tạo các giá trị mặc định
        th_origin = False
        th_source_group_id = False
        th_partner_referred_id = False
        th_channel_id = fastapi.env.ref('th_setup_parameters.th_channel_unknown').id
        th_ownership = fastapi.env['th.ownership.unit'].search(
            [('th_code', '=', lead_data.get('context').get('th_ownership_code'))], limit=1)
        check_module = fastapi.env['ir.config_parameter'].sudo().get_param('th_check_partner_module')

        # Xử lý ngành học
        th_major_id = False
        if lead_data.get('th_major_code_university'):
            major = fastapi.env['th.university.major'].search([
                ('th_major_code_university', '=', lead_data.get('th_major_code_university'))
            ], limit=1)
            if major:
                th_major_id = major.th_major_id

        # Xử lý trường
        if lead_data.get('th_warehouse_code'):
            th_origin = fastapi.env['th.origin'].search([
                ('th_code', '=', lead_data.get('th_warehouse_code'))
            ], limit=1)

        # Xử lý nguồn
        if lead_data.get('th_utm_source'):
            th_source_group = fastapi.env['th.source.group'].search([
                ('name', '=', lead_data.get('th_utm_source'))
            ], limit=1)
            if th_source_group:
                th_source_group_id = th_source_group.id
            else:
                th_source_group_id = fastapi.env['th.source.group'].create({
                    'name': lead_data.get('th_utm_source')
                }).id

        # Xử lý người giới thiệu
        if lead_data.get('th_affiliate_code'):
            th_partner_referred_id = fastapi.env['res.partner'].search([
                ('th_affiliate_code', '=', lead_data.get('th_affiliate_code'))
            ], limit=1)

        # Tìm kiếm partner
        domain = []
        if lead_data.get('th_phone'):
            domain = ['|', ('phone', '=', lead_data.get('th_phone')), ('th_phone2', '=', lead_data.get('th_phone'))]
        if lead_data.get('th_email'):
            domain = ['|',] + domain + [('email', '=', lead_data.get('th_email'))] if domain else [('email', '=', lead_data.get('th_email'))]
        if check_module:
            domain.append(('th_module_ids', 'in', fastapi.env.ref('th_setup_parameters.th_crm_module').ids))

        partner = False
        if domain:
            partner = fastapi.env['res.partner'].search(domain, limit=1)
            if partner:
                # Cập nhật thông tin phone cho partner nếu cần
                if partner.phone and not partner.th_phone2 and lead_data.get('th_phone') and partner.phone != lead_data.get('th_phone'):
                    partner.write({'th_phone2': lead_data.get('th_phone')})
                elif not partner.phone and lead_data.get('th_phone'):
                    partner.write({'phone': lead_data.get('th_phone')})
                elif partner.th_phone2 and partner.phone and lead_data.get('th_phone') and lead_data.get('th_phone') not in [partner.th_phone2, partner.phone]:
                    partner.write({
                        'comment': partner.comment + '\nSố điện thoại mới: ' + lead_data.get('th_phone') if partner.comment 
                        else 'Số điện thoại mới: ' + lead_data.get('th_phone')
                    })

        # Tạo partner mới nếu chưa tồn tại
        if not partner:
            partner = fastapi.env['res.partner'].create({
                'name': lead_data.get('th_customer') or lead_data.get('th_phone'),
                'phone': lead_data.get('th_phone'),
                'email': lead_data.get('th_email'),
                'th_check_module': True,
            })

        # Tạo cơ hội mới
        new_opportunity = {
            'partner_id': partner.id,
            'th_origin_id': th_origin.id if th_origin else fastapi.env.ref('th_setup_parameters.th_aum_university_origin').id,
            'th_major_id': th_major_id.id if th_major_id else False,
            'th_last_check': Datetime.now(),
            'th_partner_referred_id': th_partner_referred_id.id if th_partner_referred_id else False,
            'th_lead_aff_id': lead_data.get('id'),
            'th_ownership_id': th_ownership.id if th_ownership else False,
            'th_description': lead_data.get('th_description'),
            'th_source_name': lead_data.get('th_source_name'),
            'th_utm_source': lead_data.get('th_utm_source'),
            'th_utm_medium': lead_data.get('th_utm_medium'),
            'th_utm_campaign': lead_data.get('th_utm_campaign'),
            'th_utm_term': lead_data.get('th_utm_term'),
            'th_utm_content': lead_data.get('th_utm_content'),
            'th_source_group_id': th_source_group_id,
            'th_channel_id': th_channel_id,
            'th_form_name': lead_data.get('th_form_name'),
            'th_uuid_form': lead_data.get('th_uuid_form'),
        }

        # Xử lý form configuration
        if lead_data.get('context').get('th_form_id') and lead_data.get('aff_crm_lead'):
            th_formio_pro = fastapi.env['th.formio.builder.field.aff.default'].search([
                ('th_uuid', '=', lead_data.get('context').get('th_form_id'))
            ])
            if th_formio_pro:
                new_opportunity.update({
                    'th_dividing_ring_id': th_formio_pro.th_dividing_ring_id.id,
                    'th_status_group_id': th_formio_pro.th_status_group_id.id if th_formio_pro.th_status_group_id else False,
                    'th_status_detail_id': th_formio_pro.th_status_detail_id.id if th_formio_pro.th_status_detail_id else False,
                })

        # Tạo CRM Lead
        crm_lead = fastapi.env['crm.lead'].sudo().create(new_opportunity)

        # Kiểm tra trùng lặp
        result = {
            'crm_id': crm_lead.id,
            'duplicate': False
        }

        if lead_data.get('id') and crm_lead:
            exist_lead = fastapi.env['crm.lead'].sudo().search([
                ('id', '!=', crm_lead.id),
                ('th_origin_id', '=', crm_lead.th_origin_id.id),
                ('partner_id', '=', crm_lead.partner_id.id),
                ('th_is_a_duplicate_opportunity', '=', False)
            ], limit=1)

            if exist_lead:
                result['duplicate'] = True
                th_last_check = (Date.today() - exist_lead.th_last_check.date()).days
                check_condition = fastapi.env['th.check.condition'].search([
                    ('th_date_from', '<=', th_last_check),
                    ('th_date_to', '>', th_last_check),
                    ('th_crm_level_id', '=', exist_lead.stage_id.id),
                    ('th_status_detail_id', '=', exist_lead.th_status_detail_id.id)
                ], limit=1)

                if check_condition:
                    result['th_dup_state'] = 'processed'
                    result['th_selection_dup_result'] = 'keep' if check_condition.th_result == 'keep' else 'change'
                else:
                    result['th_dup_state'] = 'processing'
        result['th_processing_time'] = round(time.perf_counter() - start, 4) * 1000
        return result

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))