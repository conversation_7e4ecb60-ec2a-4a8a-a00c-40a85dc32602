{"version": 12, "sheets": [{"id": "Sheet1", "name": "Dashboard", "colNumber": 7, "rowNumber": 70, "rows": {"6": {"size": 40}, "18": {"size": 40}, "19": {"size": 40}, "31": {"size": 40}, "32": {"size": 40}, "44": {"size": 40}, "45": {"size": 40}, "57": {"size": 40}, "58": {"size": 40}}, "cols": {"0": {"size": 275}, "1": {"size": 100}, "2": {"size": 100}, "3": {"size": 50}, "4": {"size": 275}, "5": {"size": 100}, "6": {"size": 100}}, "merges": [], "cells": {"A7": {"style": 1, "content": "[Pipeline Stages](odoo://view/{\"viewType\":\"graph\",\"action\":{\"domain\":[[\"type\",\"=\",\"opportunity\"]],\"context\":{\"group_by\":[\"stage_id\",\"team_id\"],\"graph_measure\":\"prorated_revenue\",\"graph_mode\":\"bar\",\"graph_groupbys\":[\"stage_id\",\"team_id\"]},\"modelName\":\"crm.lead\",\"views\":[[false,\"kanban\"],[false,\"list\"],[false,\"calendar\"],[false,\"pivot\"],[false,\"graph\"],[false,\"map\"],[false,\"form\"],[false,\"activity\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Pipeline\",\"positional\":true})", "border": 1}, "A19": {"style": 1, "content": "[Top Opportunities](odoo://view/{\"viewType\":\"list\",\"action\":{\"domain\":\"[[\\\"type\\\", \\\"=\\\", \\\"opportunity\\\"]]\",\"context\":{\"group_by\":[]},\"modelName\":\"crm.lead\",\"views\":[[false,\"kanban\"],[false,\"list\"],[false,\"calendar\"],[false,\"pivot\"],[false,\"graph\"],[false,\"map\"],[false,\"form\"],[false,\"activity\"]]},\"threshold\":0,\"name\":\"Pipeline\",\"positional\":true})", "border": 1}, "A20": {"style": 2, "content": "=_t(\"Opportunity\")", "border": 2}, "A21": {"style": 3, "content": "=ODOO.LIST(1,1,\"name\")"}, "A22": {"style": 4, "content": "=ODOO.LIST(1,2,\"name\")"}, "A23": {"style": 3, "content": "=ODOO.LIST(1,3,\"name\")"}, "A24": {"style": 4, "content": "=ODOO.LIST(1,4,\"name\")"}, "A25": {"style": 3, "content": "=ODOO.LIST(1,5,\"name\")"}, "A26": {"style": 4, "content": "=ODOO.LIST(1,6,\"name\")"}, "A27": {"style": 3, "content": "=ODOO.LIST(1,7,\"name\")"}, "A28": {"style": 4, "content": "=ODOO.LIST(1,8,\"name\")"}, "A29": {"style": 3, "content": "=ODOO.LIST(1,9,\"name\")"}, "A30": {"style": 4, "content": "=ODOO.LIST(1,10,\"name\")"}, "A32": {"style": 1, "content": "[Top Salespeople](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[\"&\",[\"type\",\"=\",\"opportunity\"],[\"user_id\",\"!=\",false]],\"context\":{\"group_by\":[\"user_id\"],\"pivot_measures\":[\"__count\",\"prorated_revenue\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"user_id\"]},\"modelName\":\"crm.lead\",\"views\":[[false,\"kanban\"],[false,\"list\"],[false,\"calendar\"],[false,\"pivot\"],[false,\"graph\"],[false,\"map\"],[false,\"form\"],[false,\"activity\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Pipeline\",\"positional\":true})", "border": 1}, "A33": {"style": 2, "content": "=_t(\"Salesperson\")", "border": 2}, "A34": {"style": 5, "content": "=ODOO.PIVOT.HEADER(3,\"#user_id\",1)"}, "A35": {"style": 6, "content": "=ODOO.PIVOT.HEADER(3,\"#user_id\",2)"}, "A36": {"style": 5, "content": "=ODOO.PIVOT.HEADER(3,\"#user_id\",3)"}, "A37": {"style": 6, "content": "=ODOO.PIVOT.HEADER(3,\"#user_id\",4)"}, "A38": {"style": 5, "content": "=ODOO.PIVOT.HEADER(3,\"#user_id\",5)"}, "A39": {"style": 6, "content": "=ODOO.PIVOT.HEADER(3,\"#user_id\",6)"}, "A40": {"style": 5, "content": "=ODOO.PIVOT.HEADER(3,\"#user_id\",7)"}, "A41": {"style": 6, "content": "=ODOO.PIVOT.HEADER(3,\"#user_id\",8)"}, "A42": {"style": 5, "content": "=ODOO.PIVOT.HEADER(3,\"#user_id\",9)"}, "A43": {"style": 6, "content": "=ODOO.PIVOT.HEADER(3,\"#user_id\",10)"}, "A45": {"style": 1, "content": "[Top Countries](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[\"&\",[\"type\",\"=\",\"opportunity\"],[\"country_id\",\"!=\",false]],\"context\":{\"group_by\":[\"country_id\"],\"pivot_measures\":[\"__count\",\"prorated_revenue\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"country_id\"]},\"modelName\":\"crm.lead\",\"views\":[[false,\"kanban\"],[false,\"list\"],[false,\"calendar\"],[false,\"pivot\"],[false,\"graph\"],[false,\"map\"],[false,\"form\"],[false,\"activity\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Pipeline\",\"positional\":true})", "border": 1}, "A46": {"style": 2, "content": "=_t(\"Country\")", "border": 2}, "A47": {"style": 5, "content": "=ODOO.PIVOT.HEADER(5,\"#country_id\",1)"}, "A48": {"style": 6, "content": "=ODOO.PIVOT.HEADER(5,\"#country_id\",2)"}, "A49": {"style": 5, "content": "=ODOO.PIVOT.HEADER(5,\"#country_id\",3)"}, "A50": {"style": 6, "content": "=ODOO.PIVOT.HEADER(5,\"#country_id\",4)"}, "A51": {"style": 5, "content": "=ODOO.PIVOT.HEADER(5,\"#country_id\",5)"}, "A52": {"style": 6, "content": "=ODOO.PIVOT.HEADER(5,\"#country_id\",6)"}, "A53": {"style": 5, "content": "=ODOO.PIVOT.HEADER(5,\"#country_id\",7)"}, "A54": {"style": 6}, "A55": {"style": 5}, "A56": {"style": 6}, "A58": {"style": 1, "content": "[Top Mediums](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[\"&\",[\"type\",\"=\",\"opportunity\"],[\"medium_id\",\"!=\",false]],\"context\":{\"group_by\":[\"medium_id\"],\"pivot_measures\":[\"__count\",\"prorated_revenue\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"medium_id\"]},\"modelName\":\"crm.lead\",\"views\":[[false,\"kanban\"],[false,\"list\"],[false,\"calendar\"],[false,\"pivot\"],[false,\"graph\"],[false,\"map\"],[false,\"form\"],[false,\"activity\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Pipeline\",\"positional\":true})", "border": 1}, "A59": {"style": 2, "content": "=_t(\"Medium\")", "border": 2}, "A60": {"style": 5, "content": "=ODOO.PIVOT.HEADER(7,\"#medium_id\",1)"}, "A61": {"style": 6, "content": "=ODOO.PIVOT.HEADER(7,\"#medium_id\",2)"}, "A62": {"style": 5, "content": "=ODOO.PIVOT.HEADER(7,\"#medium_id\",3)"}, "A63": {"style": 6, "content": "=ODOO.PIVOT.HEADER(7,\"#medium_id\",4)"}, "A64": {"style": 5, "content": "=ODOO.PIVOT.HEADER(7,\"#medium_id\",5)"}, "A65": {"style": 6, "content": "=ODOO.PIVOT.HEADER(7,\"#medium_id\",6)"}, "A66": {"style": 5, "content": "=ODOO.PIVOT.HEADER(7,\"#medium_id\",7)"}, "A67": {"style": 6, "content": "=ODOO.PIVOT.HEADER(7,\"#medium_id\",8)"}, "A68": {"style": 5, "content": "=ODOO.PIVOT.HEADER(7,\"#medium_id\",9)"}, "A69": {"style": 6, "content": "=ODOO.PIVOT.HEADER(7,\"#medium_id\",10)"}, "B20": {"style": 2, "content": "=_t(\"Stage\")", "border": 2}, "B21": {"style": 7, "content": "=ODOO.LIST(1,1,\"stage_id\")"}, "B22": {"content": "=ODOO.LIST(1,2,\"stage_id\")"}, "B23": {"style": 7, "content": "=ODOO.LIST(1,3,\"stage_id\")"}, "B24": {"content": "=ODOO.LIST(1,4,\"stage_id\")"}, "B25": {"style": 7, "content": "=ODOO.LIST(1,5,\"stage_id\")"}, "B26": {"content": "=ODOO.LIST(1,6,\"stage_id\")"}, "B27": {"style": 7, "content": "=ODOO.LIST(1,7,\"stage_id\")"}, "B28": {"content": "=ODOO.LIST(1,8,\"stage_id\")"}, "B29": {"style": 7, "content": "=ODOO.LIST(1,9,\"stage_id\")"}, "B30": {"content": "=ODOO.LIST(1,10,\"stage_id\")"}, "B33": {"style": 8, "content": "=_t(\"# Leads\")", "border": 2}, "B34": {"style": 7, "content": "=ODOO.PIVOT(3,\"__count\",\"#user_id\",1)"}, "B35": {"content": "=ODOO.PIVOT(3,\"__count\",\"#user_id\",2)"}, "B36": {"style": 7, "content": "=ODOO.PIVOT(3,\"__count\",\"#user_id\",3)"}, "B37": {"content": "=ODOO.PIVOT(3,\"__count\",\"#user_id\",4)"}, "B38": {"style": 7, "content": "=ODOO.PIVOT(3,\"__count\",\"#user_id\",5)"}, "B39": {"content": "=ODOO.PIVOT(3,\"__count\",\"#user_id\",6)"}, "B40": {"style": 7, "content": "=ODOO.PIVOT(3,\"__count\",\"#user_id\",7)"}, "B41": {"content": "=ODOO.PIVOT(3,\"__count\",\"#user_id\",8)"}, "B42": {"style": 7, "content": "=ODOO.PIVOT(3,\"__count\",\"#user_id\",9)"}, "B43": {"content": "=ODOO.PIVOT(3,\"__count\",\"#user_id\",10)"}, "B46": {"style": 8, "content": "=_t(\"# Leads\")", "border": 2}, "B47": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(5,\"__count\",\"#country_id\",1)"}, "B48": {"format": 1, "content": "=ODOO.PIVOT(5,\"__count\",\"#country_id\",2)"}, "B49": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(5,\"__count\",\"#country_id\",3)"}, "B50": {"format": 1, "content": "=ODOO.PIVOT(5,\"__count\",\"#country_id\",4)"}, "B51": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(5,\"__count\",\"#country_id\",5)"}, "B52": {"format": 1, "content": "=ODOO.PIVOT(5,\"__count\",\"#country_id\",6)"}, "B53": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(5,\"__count\",\"#country_id\",7)"}, "B54": {"format": 1}, "B55": {"style": 7, "format": 1}, "B56": {"format": 1}, "B59": {"style": 8, "content": "=_t(\"# Leads\")", "border": 2}, "B60": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(7,\"__count\",\"#medium_id\",1)"}, "B61": {"format": 1, "content": "=ODOO.PIVOT(7,\"__count\",\"#medium_id\",2)"}, "B62": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(7,\"__count\",\"#medium_id\",3)"}, "B63": {"format": 1, "content": "=ODOO.PIVOT(7,\"__count\",\"#medium_id\",4)"}, "B64": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(7,\"__count\",\"#medium_id\",5)"}, "B65": {"format": 1, "content": "=ODOO.PIVOT(7,\"__count\",\"#medium_id\",6)"}, "B66": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(7,\"__count\",\"#medium_id\",7)"}, "B67": {"format": 1, "content": "=ODOO.PIVOT(7,\"__count\",\"#medium_id\",8)"}, "B68": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(7,\"__count\",\"#medium_id\",9)"}, "B69": {"format": 1, "content": "=ODOO.PIVOT(7,\"__count\",\"#medium_id\",10)"}, "C20": {"style": 2, "content": "=_t(\"Salesperson\")", "border": 2}, "C21": {"style": 7, "format": 2, "content": "=ODOO.LIST(1,1,\"user_id\")"}, "C22": {"format": 2, "content": "=ODOO.LIST(1,2,\"user_id\")"}, "C23": {"style": 7, "format": 2, "content": "=ODOO.LIST(1,3,\"user_id\")"}, "C24": {"format": 2, "content": "=ODOO.LIST(1,4,\"user_id\")"}, "C25": {"style": 7, "format": 2, "content": "=ODOO.LIST(1,5,\"user_id\")"}, "C26": {"format": 2, "content": "=ODOO.LIST(1,6,\"user_id\")"}, "C27": {"style": 7, "format": 2, "content": "=ODOO.LIST(1,7,\"user_id\")"}, "C28": {"format": 2, "content": "=ODOO.LIST(1,8,\"user_id\")"}, "C29": {"style": 7, "format": 2, "content": "=ODOO.LIST(1,9,\"user_id\")"}, "C30": {"format": 2, "content": "=ODOO.LIST(1,10,\"user_id\")"}, "C33": {"style": 8, "content": "=_t(\"Revenue\")", "border": 2}, "C34": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(3,\"prorated_revenue\",\"#user_id\",1)"}, "C35": {"format": 2, "content": "=ODOO.PIVOT(3,\"prorated_revenue\",\"#user_id\",2)"}, "C36": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(3,\"prorated_revenue\",\"#user_id\",3)"}, "C37": {"format": 2, "content": "=ODOO.PIVOT(3,\"prorated_revenue\",\"#user_id\",4)"}, "C38": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(3,\"prorated_revenue\",\"#user_id\",5)"}, "C39": {"format": 2, "content": "=ODOO.PIVOT(3,\"prorated_revenue\",\"#user_id\",6)"}, "C40": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(3,\"prorated_revenue\",\"#user_id\",7)"}, "C41": {"format": 2, "content": "=ODOO.PIVOT(3,\"prorated_revenue\",\"#user_id\",8)"}, "C42": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(3,\"prorated_revenue\",\"#user_id\",9)"}, "C43": {"format": 2, "content": "=ODOO.PIVOT(3,\"prorated_revenue\",\"#user_id\",10)"}, "C46": {"style": 8, "content": "=_t(\"Revenue\")", "border": 2}, "C47": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(5,\"prorated_revenue\",\"#country_id\",1)"}, "C48": {"format": 2, "content": "=ODOO.PIVOT(5,\"prorated_revenue\",\"#country_id\",2)"}, "C49": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(5,\"prorated_revenue\",\"#country_id\",3)"}, "C50": {"format": 2, "content": "=ODOO.PIVOT(5,\"prorated_revenue\",\"#country_id\",4)"}, "C51": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(5,\"prorated_revenue\",\"#country_id\",5)"}, "C52": {"format": 2, "content": "=ODOO.PIVOT(5,\"prorated_revenue\",\"#country_id\",6)"}, "C53": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(5,\"prorated_revenue\",\"#country_id\",7)"}, "C54": {"format": 2}, "C55": {"style": 7, "format": 2}, "C56": {"format": 2}, "C59": {"style": 8, "content": "=_t(\"Revenue\")", "border": 2}, "C60": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(7,\"prorated_revenue\",\"#medium_id\",1)"}, "C61": {"format": 2, "content": "=ODOO.PIVOT(7,\"prorated_revenue\",\"#medium_id\",2)"}, "C62": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(7,\"prorated_revenue\",\"#medium_id\",3)"}, "C63": {"format": 2, "content": "=ODOO.PIVOT(7,\"prorated_revenue\",\"#medium_id\",4)"}, "C64": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(7,\"prorated_revenue\",\"#medium_id\",5)"}, "C65": {"format": 2, "content": "=ODOO.PIVOT(7,\"prorated_revenue\",\"#medium_id\",6)"}, "C66": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(7,\"prorated_revenue\",\"#medium_id\",7)"}, "C67": {"format": 2, "content": "=ODOO.PIVOT(7,\"prorated_revenue\",\"#medium_id\",8)"}, "C68": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(7,\"prorated_revenue\",\"#medium_id\",9)"}, "C69": {"format": 2, "content": "=ODOO.PIVOT(7,\"prorated_revenue\",\"#medium_id\",10)"}, "D20": {"style": 2, "border": 2}, "D21": {"style": 7, "format": 1}, "D22": {"format": 1}, "D23": {"style": 7, "format": 1}, "D24": {"format": 1}, "D25": {"style": 7, "format": 1}, "D26": {"format": 1}, "D27": {"style": 7, "format": 1}, "D28": {"format": 1}, "D29": {"style": 7, "format": 1}, "D30": {"format": 1}, "E7": {"style": 1, "content": "[Expected Closing](odoo://view/{\"viewType\":\"graph\",\"action\":{\"domain\":[\"&\",[\"type\",\"=\",\"opportunity\"],[\"date_deadline\",\"!=\",false]],\"context\":{\"group_by\":[\"date_deadline:month\",\"team_id\"],\"graph_measure\":\"prorated_revenue\",\"graph_mode\":\"bar\",\"graph_groupbys\":[\"date_deadline:month\",\"team_id\"]},\"modelName\":\"crm.lead\",\"views\":[[false,\"kanban\"],[false,\"list\"],[false,\"calendar\"],[false,\"pivot\"],[false,\"graph\"],[false,\"map\"],[false,\"form\"],[false,\"activity\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Pipeline\",\"positional\":true})", "border": 1}, "E20": {"style": 2, "content": "=_t(\"Country\")", "border": 2}, "E21": {"style": 7, "format": 1, "content": "=ODOO.LIST(1,1,\"country_id\")"}, "E22": {"format": 1, "content": "=ODOO.LIST(1,2,\"country_id\")"}, "E23": {"style": 7, "format": 1, "content": "=ODOO.LIST(1,3,\"country_id\")"}, "E24": {"format": 1, "content": "=ODOO.LIST(1,4,\"country_id\")"}, "E25": {"style": 7, "format": 1, "content": "=ODOO.LIST(1,5,\"country_id\")"}, "E26": {"format": 1, "content": "=ODOO.LIST(1,6,\"country_id\")"}, "E27": {"style": 7, "format": 1, "content": "=ODOO.LIST(1,7,\"country_id\")"}, "E28": {"format": 1, "content": "=ODOO.LIST(1,8,\"country_id\")"}, "E29": {"style": 7, "format": 1, "content": "=ODOO.LIST(1,9,\"country_id\")"}, "E30": {"format": 1, "content": "=ODOO.LIST(1,10,\"country_id\")"}, "E32": {"style": 1, "content": "[Top Sales Teams](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[\"&\",[\"type\",\"=\",\"opportunity\"],[\"team_id\",\"!=\",false]],\"context\":{\"group_by\":[\"team_id\"],\"pivot_measures\":[\"__count\",\"prorated_revenue\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"team_id\"]},\"modelName\":\"crm.lead\",\"views\":[[false,\"kanban\"],[false,\"list\"],[false,\"calendar\"],[false,\"pivot\"],[false,\"graph\"],[false,\"map\"],[false,\"form\"],[false,\"activity\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Pipeline\",\"positional\":true})", "border": 1}, "E33": {"style": 2, "content": "=_t(\"Sales Team\")", "border": 2}, "E34": {"style": 5, "content": "=ODOO.PIVOT.HEADER(4,\"#team_id\",1)"}, "E35": {"style": 6, "content": "=ODOO.PIVOT.HEADER(4,\"#team_id\",2)"}, "E36": {"style": 5, "content": "=ODOO.PIVOT.HEADER(4,\"#team_id\",3)"}, "E37": {"style": 6, "content": "=ODOO.PIVOT.HEADER(4,\"#team_id\",4)"}, "E38": {"style": 5, "content": "=ODOO.PIVOT.HEADER(4,\"#team_id\",5)"}, "E39": {"style": 6, "content": "=ODOO.PIVOT.HEADER(4,\"#team_id\",6)"}, "E40": {"style": 5, "content": "=ODOO.PIVOT.HEADER(4,\"#team_id\",7)"}, "E41": {"style": 6, "content": "=ODOO.PIVOT.HEADER(4,\"#team_id\",8)"}, "E42": {"style": 5, "content": "=ODOO.PIVOT.HEADER(4,\"#team_id\",9)"}, "E43": {"style": 6, "content": "=ODOO.PIVOT.HEADER(4,\"#team_id\",10)"}, "E45": {"style": 1, "content": "[Top Cities](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[\"&\",[\"type\",\"=\",\"opportunity\"],[\"city\",\"!=\",false]],\"context\":{\"group_by\":[\"city\"],\"pivot_measures\":[\"__count\",\"prorated_revenue\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"city\"]},\"modelName\":\"crm.lead\",\"views\":[[false,\"kanban\"],[false,\"list\"],[false,\"calendar\"],[false,\"pivot\"],[false,\"graph\"],[false,\"map\"],[false,\"form\"],[false,\"activity\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Pipeline\",\"positional\":true})", "border": 1}, "E46": {"style": 2, "content": "=_t(\"City\")", "border": 2}, "E47": {"style": 5, "content": "=ODOO.PIVOT.HEADER(6,\"#city\",1)"}, "E48": {"style": 6, "content": "=ODOO.PIVOT.HEADER(6,\"#city\",2)"}, "E49": {"style": 5, "content": "=ODOO.PIVOT.HEADER(6,\"#city\",3)"}, "E50": {"style": 6, "content": "=ODOO.PIVOT.HEADER(6,\"#city\",4)"}, "E51": {"style": 5, "content": "=ODOO.PIVOT.HEADER(6,\"#city\",5)"}, "E52": {"style": 6, "content": "=ODOO.PIVOT.HEADER(6,\"#city\",6)"}, "E53": {"style": 5, "content": "=ODOO.PIVOT.HEADER(6,\"#city\",7)"}, "E54": {"style": 6, "content": "=ODOO.PIVOT.HEADER(6,\"#city\",8)"}, "E55": {"style": 5, "content": "=ODOO.PIVOT.HEADER(6,\"#city\",9)"}, "E56": {"style": 6, "content": "=ODOO.PIVOT.HEADER(6,\"#city\",10)"}, "E58": {"style": 1, "content": "[Top Sources](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[\"&\",[\"type\",\"=\",\"opportunity\"],[\"source_id\",\"!=\",false]],\"context\":{\"group_by\":[\"source_id\"],\"pivot_measures\":[\"__count\",\"prorated_revenue\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"source_id\"]},\"modelName\":\"crm.lead\",\"views\":[[false,\"kanban\"],[false,\"list\"],[false,\"calendar\"],[false,\"pivot\"],[false,\"graph\"],[false,\"map\"],[false,\"form\"],[false,\"activity\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Pipeline\",\"positional\":true})", "border": 1}, "E59": {"style": 2, "content": "=_t(\"Source\")", "border": 2}, "E60": {"style": 5, "content": "=ODOO.PIVOT.HEADER(8,\"#source_id\",1)"}, "E61": {"style": 6, "content": "=ODOO.PIVOT.HEADER(8,\"#source_id\",2)"}, "E62": {"style": 5, "content": "=ODOO.PIVOT.HEADER(8,\"#source_id\",3)"}, "E63": {"style": 6, "content": "=ODOO.PIVOT.HEADER(8,\"#source_id\",4)"}, "E64": {"style": 5, "content": "=ODOO.PIVOT.HEADER(8,\"#source_id\",5)"}, "E65": {"style": 6, "content": "=ODOO.PIVOT.HEADER(8,\"#source_id\",6)"}, "E66": {"style": 5, "content": "=ODOO.PIVOT.HEADER(8,\"#source_id\",7)"}, "E67": {"style": 6, "content": "=ODOO.PIVOT.HEADER(8,\"#source_id\",8)"}, "E68": {"style": 5, "content": "=ODOO.PIVOT.HEADER(8,\"#source_id\",9)"}, "E69": {"style": 6, "content": "=ODOO.PIVOT.HEADER(8,\"#source_id\",10)"}, "F20": {"style": 8, "content": "=_t(\"Revenue\")", "border": 2}, "F21": {"style": 7, "content": "=ODOO.LIST(1,1,\"prorated_revenue\")"}, "F22": {"content": "=ODOO.LIST(1,2,\"prorated_revenue\")"}, "F23": {"style": 7, "content": "=ODOO.LIST(1,3,\"prorated_revenue\")"}, "F24": {"content": "=ODOO.LIST(1,4,\"prorated_revenue\")"}, "F25": {"style": 7, "content": "=ODOO.LIST(1,5,\"prorated_revenue\")"}, "F26": {"content": "=ODOO.LIST(1,6,\"prorated_revenue\")"}, "F27": {"style": 7, "content": "=ODOO.LIST(1,7,\"prorated_revenue\")"}, "F28": {"content": "=ODOO.LIST(1,8,\"prorated_revenue\")"}, "F29": {"style": 7, "content": "=ODOO.LIST(1,9,\"prorated_revenue\")"}, "F30": {"content": "=ODOO.LIST(1,10,\"prorated_revenue\")"}, "F33": {"style": 8, "content": "=_t(\"# Leads\")", "border": 2}, "F34": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(4,\"__count\",\"#team_id\",1)"}, "F35": {"format": 1, "content": "=ODOO.PIVOT(4,\"__count\",\"#team_id\",2)"}, "F36": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(4,\"__count\",\"#team_id\",3)"}, "F37": {"format": 1, "content": "=ODOO.PIVOT(4,\"__count\",\"#team_id\",4)"}, "F38": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(4,\"__count\",\"#team_id\",5)"}, "F39": {"format": 1, "content": "=ODOO.PIVOT(4,\"__count\",\"#team_id\",6)"}, "F40": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(4,\"__count\",\"#team_id\",7)"}, "F41": {"format": 1, "content": "=ODOO.PIVOT(4,\"__count\",\"#team_id\",8)"}, "F42": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(4,\"__count\",\"#team_id\",9)"}, "F43": {"format": 1, "content": "=ODOO.PIVOT(4,\"__count\",\"#team_id\",10)"}, "F46": {"style": 8, "content": "=_t(\"# Leads\")", "border": 2}, "F47": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(6,\"__count\",\"#city\",1)"}, "F48": {"format": 1, "content": "=ODOO.PIVOT(6,\"__count\",\"#city\",2)"}, "F49": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(6,\"__count\",\"#city\",3)"}, "F50": {"format": 1, "content": "=ODOO.PIVOT(6,\"__count\",\"#city\",4)"}, "F51": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(6,\"__count\",\"#city\",5)"}, "F52": {"format": 1, "content": "=ODOO.PIVOT(6,\"__count\",\"#city\",6)"}, "F53": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(6,\"__count\",\"#city\",7)"}, "F54": {"format": 1, "content": "=ODOO.PIVOT(6,\"__count\",\"#city\",8)"}, "F55": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(6,\"__count\",\"#city\",9)"}, "F56": {"format": 1, "content": "=ODOO.PIVOT(6,\"__count\",\"#city\",10)"}, "F59": {"style": 8, "content": "=_t(\"# Leads\")", "border": 2}, "F60": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(8,\"__count\",\"#source_id\",1)"}, "F61": {"format": 1, "content": "=ODOO.PIVOT(8,\"__count\",\"#source_id\",2)"}, "F62": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(8,\"__count\",\"#source_id\",3)"}, "F63": {"format": 1, "content": "=ODOO.PIVOT(8,\"__count\",\"#source_id\",4)"}, "F64": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(8,\"__count\",\"#source_id\",5)"}, "F65": {"format": 1, "content": "=ODOO.PIVOT(8,\"__count\",\"#source_id\",6)"}, "F66": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(8,\"__count\",\"#source_id\",7)"}, "F67": {"format": 1, "content": "=ODOO.PIVOT(8,\"__count\",\"#source_id\",8)"}, "F68": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(8,\"__count\",\"#source_id\",9)"}, "F69": {"format": 1, "content": "=ODOO.PIVOT(8,\"__count\",\"#source_id\",10)"}, "G20": {"style": 8, "content": "Success (%)", "border": 2}, "G21": {"style": 7, "format": 2, "content": "=ODOO.LIST(1,1,\"probability\")"}, "G22": {"format": 2, "content": "=ODOO.LIST(1,2,\"probability\")"}, "G23": {"style": 7, "format": 2, "content": "=ODOO.LIST(1,3,\"probability\")"}, "G24": {"format": 2, "content": "=ODOO.LIST(1,4,\"probability\")"}, "G25": {"style": 7, "format": 2, "content": "=ODOO.LIST(1,5,\"probability\")"}, "G26": {"format": 2, "content": "=ODOO.LIST(1,6,\"probability\")"}, "G27": {"style": 7, "format": 2, "content": "=ODOO.LIST(1,7,\"probability\")"}, "G28": {"format": 2, "content": "=ODOO.LIST(1,8,\"probability\")"}, "G29": {"style": 7, "format": 2, "content": "=ODOO.LIST(1,9,\"probability\")"}, "G30": {"format": 2, "content": "=ODOO.LIST(1,10,\"probability\")"}, "G33": {"style": 8, "content": "=_t(\"Revenue\")", "border": 2}, "G34": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(4,\"prorated_revenue\",\"#team_id\",1)"}, "G35": {"format": 2, "content": "=ODOO.PIVOT(4,\"prorated_revenue\",\"#team_id\",2)"}, "G36": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(4,\"prorated_revenue\",\"#team_id\",3)"}, "G37": {"format": 2, "content": "=ODOO.PIVOT(4,\"prorated_revenue\",\"#team_id\",4)"}, "G38": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(4,\"prorated_revenue\",\"#team_id\",5)"}, "G39": {"format": 2, "content": "=ODOO.PIVOT(4,\"prorated_revenue\",\"#team_id\",6)"}, "G40": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(4,\"prorated_revenue\",\"#team_id\",7)"}, "G41": {"format": 2, "content": "=ODOO.PIVOT(4,\"prorated_revenue\",\"#team_id\",8)"}, "G42": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(4,\"prorated_revenue\",\"#team_id\",9)"}, "G43": {"format": 2, "content": "=ODOO.PIVOT(4,\"prorated_revenue\",\"#team_id\",10)"}, "G46": {"style": 8, "content": "=_t(\"Revenue\")", "border": 2}, "G47": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(6,\"prorated_revenue\",\"#city\",1)"}, "G48": {"format": 2, "content": "=ODOO.PIVOT(6,\"prorated_revenue\",\"#city\",2)"}, "G49": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(6,\"prorated_revenue\",\"#city\",3)"}, "G50": {"format": 2, "content": "=ODOO.PIVOT(6,\"prorated_revenue\",\"#city\",4)"}, "G51": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(6,\"prorated_revenue\",\"#city\",5)"}, "G52": {"format": 2, "content": "=ODOO.PIVOT(6,\"prorated_revenue\",\"#city\",6)"}, "G53": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(6,\"prorated_revenue\",\"#city\",7)"}, "G54": {"format": 2, "content": "=ODOO.PIVOT(6,\"prorated_revenue\",\"#city\",8)"}, "G55": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(6,\"prorated_revenue\",\"#city\",9)"}, "G56": {"format": 2, "content": "=ODOO.PIVOT(6,\"prorated_revenue\",\"#city\",10)"}, "G59": {"style": 8, "content": "=_t(\"Revenue\")", "border": 2}, "G60": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(8,\"prorated_revenue\",\"#source_id\",1)"}, "G61": {"format": 2, "content": "=ODOO.PIVOT(8,\"prorated_revenue\",\"#source_id\",2)"}, "G62": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(8,\"prorated_revenue\",\"#source_id\",3)"}, "G63": {"format": 2, "content": "=ODOO.PIVOT(8,\"prorated_revenue\",\"#source_id\",4)"}, "G64": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(8,\"prorated_revenue\",\"#source_id\",5)"}, "G65": {"format": 2, "content": "=ODOO.PIVOT(8,\"prorated_revenue\",\"#source_id\",6)"}, "G66": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(8,\"prorated_revenue\",\"#source_id\",7)"}, "G67": {"format": 2, "content": "=ODOO.PIVOT(8,\"prorated_revenue\",\"#source_id\",8)"}, "G68": {"style": 7, "format": 2, "content": "=ODOO.PIVOT(8,\"prorated_revenue\",\"#source_id\",9)"}, "G69": {"format": 2, "content": "=ODOO.PIVOT(8,\"prorated_revenue\",\"#source_id\",10)"}, "A8": {"border": 2}, "B7": {"border": 1}, "B8": {"border": 2}, "B19": {"border": 1}, "B32": {"border": 1}, "B45": {"border": 1}, "B58": {"border": 1}, "C7": {"border": 1}, "C8": {"border": 2}, "C19": {"border": 1}, "C32": {"border": 1}, "C45": {"border": 1}, "C58": {"border": 1}, "D19": {"border": 1}, "E8": {"border": 2}, "E19": {"border": 1}, "F7": {"border": 1}, "F8": {"border": 2}, "F19": {"border": 1}, "F32": {"border": 1}, "F45": {"border": 1}, "F58": {"border": 1}, "G7": {"border": 1}, "G8": {"border": 2}, "G19": {"border": 1}, "G32": {"border": 1}, "G45": {"border": 1}, "G58": {"border": 1}}, "conditionalFormats": [], "figures": [{"id": "09ab3fe3-04d6-4c9f-97ff-bb37fee0e692", "x": 0, "y": 0, "width": 200, "height": 120, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "percentage", "title": "Expected", "type": "scorecard", "background": "", "baseline": "Data!E4", "baselineDescr": "since last period", "keyValue": "Data!D4"}}, {"id": "5dc98740-8fc9-432d-b386-59e6e5c8b7e8", "x": 210, "y": 0, "width": 200, "height": 120, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "percentage", "title": "Closed", "type": "scorecard", "background": "", "baseline": "Data!E5", "baselineDescr": "since last period", "keyValue": "Data!D5"}}, {"id": "735dabd8-96dc-44a1-9871-f35a94c347f5", "x": 420, "y": 0, "width": 200, "height": 120, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "text", "title": "Open opportunities", "type": "scorecard", "background": "", "baselineDescr": "to close", "keyValue": "Data!D7"}}, {"id": "5adf5fa8-e0e4-4e13-a3ac-259d67389cfb", "x": 0, "y": 178, "width": 475, "height": 230, "tag": "chart", "data": {"title": "", "id": "5adf5fa8-e0e4-4e13-a3ac-259d67389cfb", "background": "#FFFFFF", "legendPosition": "none", "metaData": {"groupBy": ["stage_id", "team_id"], "measure": "prorated_revenue", "order": null, "resModel": "crm.lead"}, "searchParams": {"comparison": null, "context": {"default_type": "opportunity", "search_default_assigned_to_me": 1, "default_team_id": 1}, "domain": [["type", "=", "opportunity"]], "groupBy": ["stage_id", "team_id"], "orderBy": []}, "type": "odoo_bar", "verticalAxisPosition": "left", "stacked": true}}, {"id": "6c739756-da1e-4e9b-bd26-e74e6cd10c88", "x": 525, "y": 178, "width": 475, "height": 230, "tag": "chart", "data": {"title": "", "id": "6c739756-da1e-4e9b-bd26-e74e6cd10c88", "background": "#FFFFFF", "legendPosition": "none", "metaData": {"groupBy": ["date_deadline:month", "team_id"], "measure": "prorated_revenue", "order": null, "resModel": "crm.lead"}, "searchParams": {"comparison": null, "context": {"default_type": "opportunity", "search_default_assigned_to_me": 1, "default_team_id": 1}, "domain": ["&", ["type", "=", "opportunity"], ["date_deadline", "!=", false]], "groupBy": ["date_deadline:month", "team_id"], "orderBy": []}, "type": "odoo_bar", "verticalAxisPosition": "left", "stacked": true}}], "areGridLinesVisible": true, "isVisible": true}, {"id": "a51634aa-7103-45b3-ab61-fd26c0824a1f", "name": "Data", "colNumber": 26, "rowNumber": 102, "rows": {}, "cols": {"0": {"size": 121.92431640625}, "1": {"size": 140.92431640625}, "2": {"size": 140.92431640625}, "3": {"size": 140.92431640625}, "4": {"size": 140.92431640625}}, "merges": [], "cells": {"A1": {"style": 2, "content": "=_t(\"KPI\")"}, "A2": {"content": "=_t(\"Expected count\")"}, "A3": {"content": "=_t(\"Closed count\")"}, "A4": {"content": "=_t(\"Expected revenue\")"}, "A5": {"content": "=_t(\"Closed revenue\")"}, "A6": {"content": "=_t(\"Percentage closed\")"}, "A7": {"content": "=_t(\"To close\")"}, "B1": {"style": 2, "content": "=_t(\"Current\")"}, "B2": {"content": "=ODOO.PIVOT(1,\"__count\")"}, "B3": {"content": "=ODOO.PIVOT(1,\"__count\",\"won_status\",\"won\")"}, "B4": {"content": "=ODOO.PIVOT(1,\"prorated_revenue\")"}, "B5": {"content": "=ODOO.PIVOT(1,\"prorated_revenue\",\"won_status\",\"won\")"}, "B6": {"format": 4, "content": "=IFERROR(B3/B2)"}, "B7": {"content": "=ODOO.PIVOT(1,\"__count\",\"won_status\",\"pending\")"}, "C1": {"style": 2, "content": "=_t(\"Previous\")"}, "C2": {"content": "=ODOO.PIVOT(2,\"__count\")"}, "C3": {"content": "=ODOO.PIVOT(2,\"__count\",\"won_status\",\"won\")"}, "C4": {"content": "=ODOO.PIVOT(2,\"prorated_revenue\")"}, "C5": {"content": "=ODOO.PIVOT(2,\"prorated_revenue\",\"won_status\",\"won\")"}, "C6": {"format": 4, "content": "=IFERROR(C3/C2)"}, "C7": {"content": "=ODOO.PIVOT(2,\"__count\",\"won_status\",\"pending\")"}, "D1": {"style": 2, "content": "=_t(\"Current\")"}, "D2": {"style": 7, "content": "=FORMAT.LARGE.NUMBER(B2)"}, "D3": {"style": 7, "content": "=FORMAT.LARGE.NUMBER(B3)"}, "D4": {"style": 7, "content": "=FORMAT.LARGE.NUMBER(B4)"}, "D5": {"style": 7, "content": "=FORMAT.LARGE.NUMBER(B5)"}, "D6": {"style": 7, "format": 4, "content": "=B6"}, "D7": {"style": 7, "content": "=FORMAT.LARGE.NUMBER(B7)"}, "E1": {"style": 2, "content": "=_t(\"Previous\")"}, "E2": {"style": 7, "content": "=FORMAT.LARGE.NUMBER(C2)"}, "E3": {"style": 7, "content": "=FORMAT.LARGE.NUMBER(C3)"}, "E4": {"style": 7, "content": "=FORMAT.LARGE.NUMBER(C4)"}, "E5": {"style": 7, "content": "=FORMAT.LARGE.NUMBER(C5)"}, "E6": {"style": 7, "format": 4, "content": "=C6"}, "E7": {"style": 7, "content": "=FORMAT.LARGE.NUMBER(C7)"}, "F1": {"style": 2}, "G1": {"style": 2}, "H1": {"style": 2}, "I1": {"style": 2}, "J1": {"style": 2}, "K1": {"style": 2}, "L1": {"style": 2}, "M1": {"style": 2}, "N1": {"style": 2}, "O1": {"style": 2}, "P1": {"style": 2}, "Q1": {"style": 2}, "R1": {"style": 2}, "S1": {"style": 2}, "T1": {"style": 2}, "U1": {"style": 2}, "V1": {"style": 2}, "W1": {"style": 2}, "X1": {"style": 2}, "Y1": {"style": 2}, "Z1": {"style": 2}}, "conditionalFormats": [], "figures": [], "areGridLinesVisible": true, "isVisible": true}], "entities": {}, "styles": {"1": {"textColor": "#01666b", "bold": true, "fontSize": 16}, "2": {"bold": true}, "3": {"fillColor": "#f8f9fa", "textColor": "#01666b"}, "4": {"textColor": "#01666b"}, "5": {"fillColor": "#f8f9fa", "textColor": "#741b47"}, "6": {"textColor": "#741b47"}, "7": {"fillColor": "#f8f9fa"}, "8": {"bold": true, "align": "right"}}, "formats": {"1": "0", "2": "#,##0", "3": "[$$]#,##0", "4": "0%"}, "borders": {"1": {"bottom": ["thin", "#000"]}, "2": {"top": ["thin", "#000"]}}, "revisionId": "START_REVISION", "chartOdooMenusReferences": {"5adf5fa8-e0e4-4e13-a3ac-259d67389cfb": "crm.crm_menu_root", "6c739756-da1e-4e9b-bd26-e74e6cd10c88": "crm.crm_menu_root", "09ab3fe3-04d6-4c9f-97ff-bb37fee0e692": "crm.crm_opportunity_report_menu", "5dc98740-8fc9-432d-b386-59e6e5c8b7e8": "crm.crm_opportunity_report_menu", "735dabd8-96dc-44a1-9871-f35a94c347f5": "crm.menu_crm_opportunities"}, "odooVersion": 4, "lists": {"1": {"columns": ["name", "email_from", "phone", "country_id", "user_id", "activity_ids", "my_activity_date_deadline", "prorated_revenue", "stage_id", "probability"], "domain": [["type", "=", "opportunity"]], "model": "crm.lead", "context": {"default_type": "opportunity", "search_default_assigned_to_me": 1, "default_team_id": 1}, "orderBy": [{"name": "prorated_revenue", "asc": false}], "id": "1", "name": "list opps"}}, "listNextId": 2, "pivots": {"1": {"colGroupBys": [], "context": {"default_type": "opportunity", "search_default_assigned_to_me": 1, "default_team_id": 1}, "domain": [["type", "=", "opportunity"]], "id": "1", "measures": [{"field": "__count"}, {"field": "prorated_revenue"}], "model": "crm.lead", "rowGroupBys": ["won_status"], "name": "expected - current", "sortedColumn": null}, "2": {"colGroupBys": [], "context": {"default_type": "opportunity", "search_default_assigned_to_me": 1, "default_team_id": 1}, "domain": [["type", "=", "opportunity"]], "id": "2", "measures": [{"field": "__count"}, {"field": "prorated_revenue"}], "model": "crm.lead", "rowGroupBys": ["won_status"], "name": "expected - previous", "sortedColumn": null}, "3": {"colGroupBys": [], "context": {"default_type": "opportunity", "search_default_assigned_to_me": 1, "default_team_id": 1}, "domain": ["&", ["type", "=", "opportunity"], ["user_id", "!=", false]], "id": "3", "measures": [{"field": "__count"}, {"field": "prorated_revenue"}], "model": "crm.lead", "rowGroupBys": ["user_id"], "name": "top salespeople", "sortedColumn": {"groupId": [[], []], "measure": "prorated_revenue", "order": "desc"}}, "4": {"colGroupBys": [], "context": {"default_type": "opportunity", "search_default_assigned_to_me": 1, "default_team_id": 1}, "domain": ["&", ["type", "=", "opportunity"], ["team_id", "!=", false]], "id": "4", "measures": [{"field": "__count"}, {"field": "prorated_revenue"}], "model": "crm.lead", "rowGroupBys": ["team_id"], "name": "top sales team", "sortedColumn": {"groupId": [[], []], "measure": "prorated_revenue", "order": "desc"}}, "5": {"colGroupBys": [], "context": {"default_type": "opportunity", "search_default_assigned_to_me": 1, "default_team_id": 1}, "domain": ["&", ["type", "=", "opportunity"], ["country_id", "!=", false]], "id": "5", "measures": [{"field": "__count"}, {"field": "prorated_revenue"}], "model": "crm.lead", "rowGroupBys": ["country_id"], "name": "top countries", "sortedColumn": {"groupId": [[], []], "measure": "prorated_revenue", "order": "desc"}}, "6": {"colGroupBys": [], "context": {"default_type": "opportunity", "search_default_assigned_to_me": 1, "default_team_id": 1}, "domain": ["&", ["type", "=", "opportunity"], ["city", "!=", false]], "id": "6", "measures": [{"field": "__count"}, {"field": "prorated_revenue"}], "model": "crm.lead", "rowGroupBys": ["city"], "name": "top cities", "sortedColumn": {"groupId": [[], []], "measure": "prorated_revenue", "order": "desc"}}, "7": {"colGroupBys": [], "context": {"default_type": "opportunity", "search_default_assigned_to_me": 1, "default_team_id": 1}, "domain": ["&", ["type", "=", "opportunity"], ["medium_id", "!=", false]], "id": "7", "measures": [{"field": "__count"}, {"field": "prorated_revenue"}], "model": "crm.lead", "rowGroupBys": ["medium_id"], "name": "top mediums", "sortedColumn": {"groupId": [[], []], "measure": "prorated_revenue", "order": "desc"}}, "8": {"colGroupBys": [], "context": {"default_type": "opportunity", "search_default_assigned_to_me": 1, "default_team_id": 1}, "domain": ["&", ["type", "=", "opportunity"], ["source_id", "!=", false]], "id": "8", "measures": [{"field": "__count"}, {"field": "prorated_revenue"}], "model": "crm.lead", "rowGroupBys": ["source_id"], "name": "top sources", "sortedColumn": {"groupId": [[], []], "measure": "prorated_revenue", "order": "desc"}}}, "pivotNextId": 9, "globalFilters": [{"id": "bf6dcdb0-d22d-468c-b6a9-64f717005043", "type": "date", "label": "Period", "defaultValue": {}, "rangeType": "relative", "defaultsToCurrentPeriod": false, "pivotFields": {"1": {"field": "date_deadline", "type": "date", "offset": 0}, "2": {"field": "date_deadline", "type": "date", "offset": -1}, "3": {"field": "date_deadline", "type": "date", "offset": 0}, "4": {"field": "date_deadline", "type": "date", "offset": 0}, "5": {"field": "date_deadline", "type": "date", "offset": 0}, "6": {"field": "date_deadline", "type": "date", "offset": 0}, "7": {"field": "date_deadline", "type": "date", "offset": 0}, "8": {"field": "date_deadline", "type": "date", "offset": 0}}, "listFields": {"1": {"field": "date_deadline", "type": "date", "offset": 0}}, "graphFields": {"5adf5fa8-e0e4-4e13-a3ac-259d67389cfb": {"field": "date_deadline", "type": "date", "offset": 0}, "6c739756-da1e-4e9b-bd26-e74e6cd10c88": {"field": "date_deadline", "type": "date", "offset": 0}}}, {"id": "9f3d4602-0b4b-4eb4-b013-959d72af88ab", "type": "relation", "label": "Stage", "modelName": "crm.stage", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"1": {"field": "stage_id", "type": "many2one"}, "2": {"field": "stage_id", "type": "many2one"}, "3": {"field": "stage_id", "type": "many2one"}, "4": {"field": "stage_id", "type": "many2one"}, "5": {"field": "stage_id", "type": "many2one"}, "6": {"field": "stage_id", "type": "many2one"}, "7": {"field": "stage_id", "type": "many2one"}, "8": {"field": "stage_id", "type": "many2one"}}, "listFields": {"1": {"field": "stage_id", "type": "many2one"}}, "graphFields": {"5adf5fa8-e0e4-4e13-a3ac-259d67389cfb": {"field": "stage_id", "type": "many2one"}, "6c739756-da1e-4e9b-bd26-e74e6cd10c88": {"field": "stage_id", "type": "many2one"}}}, {"id": "9c8a3e99-903a-40a3-8799-5eaf098b884f", "type": "relation", "label": "Salesperson", "modelName": "res.users", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"1": {"field": "user_id", "type": "many2one"}, "2": {"field": "user_id", "type": "many2one"}, "3": {"field": "user_id", "type": "many2one"}, "4": {"field": "user_id", "type": "many2one"}, "5": {"field": "user_id", "type": "many2one"}, "6": {"field": "user_id", "type": "many2one"}, "7": {"field": "user_id", "type": "many2one"}, "8": {"field": "user_id", "type": "many2one"}}, "listFields": {"1": {"field": "user_id", "type": "many2one"}}, "graphFields": {"5adf5fa8-e0e4-4e13-a3ac-259d67389cfb": {"field": "user_id", "type": "many2one"}, "6c739756-da1e-4e9b-bd26-e74e6cd10c88": {"field": "user_id", "type": "many2one"}}}, {"id": "aaba8a27-cf83-4397-a9ad-a85bba4c8016", "type": "relation", "label": "Sales Team", "modelName": "crm.team", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"1": {"field": "team_id", "type": "many2one"}, "2": {"field": "team_id", "type": "many2one"}, "3": {"field": "team_id", "type": "many2one"}, "4": {"field": "team_id", "type": "many2one"}, "5": {"field": "team_id", "type": "many2one"}, "6": {"field": "team_id", "type": "many2one"}, "7": {"field": "team_id", "type": "many2one"}, "8": {"field": "team_id", "type": "many2one"}}, "listFields": {"1": {"field": "team_id", "type": "many2one"}}, "graphFields": {"5adf5fa8-e0e4-4e13-a3ac-259d67389cfb": {"field": "team_id", "type": "many2one"}, "6c739756-da1e-4e9b-bd26-e74e6cd10c88": {"field": "team_id", "type": "many2one"}}}, {"id": "f6ef716d-3135-4963-b645-bc12c6a3421b", "type": "relation", "label": "Country", "modelName": "res.country", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"1": {"field": "country_id", "type": "many2one"}, "2": {"field": "country_id", "type": "many2one"}, "3": {"field": "country_id", "type": "many2one"}, "4": {"field": "country_id", "type": "many2one"}, "5": {"field": "country_id", "type": "many2one"}, "6": {"field": "country_id", "type": "many2one"}, "7": {"field": "country_id", "type": "many2one"}, "8": {"field": "country_id", "type": "many2one"}}, "listFields": {"1": {"field": "country_id", "type": "many2one"}}, "graphFields": {"5adf5fa8-e0e4-4e13-a3ac-259d67389cfb": {"field": "country_id", "type": "many2one"}, "6c739756-da1e-4e9b-bd26-e74e6cd10c88": {"field": "country_id", "type": "many2one"}}}, {"id": "ec772492-b1db-4a63-be55-a4e0c3167edf", "type": "text", "label": "City", "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"1": {"field": "city", "type": "char"}, "2": {"field": "city", "type": "char"}, "3": {"field": "city", "type": "char"}, "4": {"field": "city", "type": "char"}, "5": {"field": "city", "type": "char"}, "6": {"field": "city", "type": "char"}, "7": {"field": "city", "type": "char"}, "8": {"field": "city", "type": "char"}}, "listFields": {"1": {"field": "city", "type": "char"}}, "graphFields": {"5adf5fa8-e0e4-4e13-a3ac-259d67389cfb": {"field": "city", "type": "char"}, "6c739756-da1e-4e9b-bd26-e74e6cd10c88": {"field": "city", "type": "char"}}}, {"id": "c21a4660-9a0a-4757-8d28-1c8d95f73edb", "type": "relation", "label": "Medium", "modelName": "utm.medium", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"1": {"field": "medium_id", "type": "many2one"}, "2": {"field": "medium_id", "type": "many2one"}, "3": {"field": "medium_id", "type": "many2one"}, "4": {"field": "medium_id", "type": "many2one"}, "5": {"field": "medium_id", "type": "many2one"}, "6": {"field": "medium_id", "type": "many2one"}, "7": {"field": "medium_id", "type": "many2one"}, "8": {"field": "medium_id", "type": "many2one"}}, "listFields": {"1": {"field": "medium_id", "type": "many2one"}}, "graphFields": {"5adf5fa8-e0e4-4e13-a3ac-259d67389cfb": {"field": "medium_id", "type": "many2one"}, "6c739756-da1e-4e9b-bd26-e74e6cd10c88": {"field": "medium_id", "type": "many2one"}}}, {"id": "c33d6d0a-df2d-4150-86b0-112cd250ddbd", "type": "relation", "label": "Source", "modelName": "utm.source", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"1": {"field": "source_id", "type": "many2one"}, "2": {"field": "source_id", "type": "many2one"}, "3": {"field": "source_id", "type": "many2one"}, "4": {"field": "source_id", "type": "many2one"}, "5": {"field": "source_id", "type": "many2one"}, "6": {"field": "source_id", "type": "many2one"}, "7": {"field": "source_id", "type": "many2one"}, "8": {"field": "source_id", "type": "many2one"}}, "listFields": {"1": {"field": "source_id", "type": "many2one"}}, "graphFields": {"5adf5fa8-e0e4-4e13-a3ac-259d67389cfb": {"field": "source_id", "type": "many2one"}, "6c739756-da1e-4e9b-bd26-e74e6cd10c88": {"field": "source_id", "type": "many2one"}}}]}