{"version": 12, "sheets": [{"id": "Sheet1", "name": "Dashboard", "colNumber": 4, "rowNumber": 40, "rows": {}, "cols": {"0": {"size": 225}, "1": {"size": 445}, "2": {"size": 225}, "3": {"size": 445}}, "merges": [], "cells": {"B1": {"style": 1}, "B2": {"style": 1, "content": "=_t(\"(Net sales – COGS) / Net sales\")"}, "B3": {"content": "=_t(\"> 50%: hugely profitable business\")"}, "B4": {"content": "=_t(\"< 20%: hard to become profitable\")"}, "B5": {"content": "=_t(\"possible issue in the business model\")"}, "B10": {"style": 1, "content": "=_t(\"EBIT / Net sales\")"}, "B11": {"content": "=_t(\"< 5%: not efficient at operating business\")"}, "B12": {"content": "=_t(\"> 10%: very efficient at operating business\")"}, "B13": {"content": "=_t(\"possible issue in COGS (Cost of Goods sold)\")"}, "B18": {"style": 1, "content": "=_t(\"Current assets / Current liabilities\")"}, "B19": {"content": "=_t(\"> 1.5: strong financial performance\")"}, "B20": {"content": "=_t(\"< 1: weak financial performance\")"}, "B21": {"content": "=_t(\"possible issue with asset distribution and cash availability\")"}, "B26": {"style": 1, "content": "=_t(\"Current assets – Current liabilities\")"}, "B27": {"content": "=_t(\"> 0: company can meet financial obligations at any time\")"}, "B28": {"content": "=_t(\"< 0: company might not be able to meet obligations\")"}, "B29": {"content": "=_t(\"possible issues in cash availability at short term\")"}, "B34": {"style": 1, "content": "=_t(\"Net Credit Purchases / Average accounts payable balance for period\")"}, "B35": {"content": "=_t(\"> 3: company liquidates debts to suppliers quickly\")"}, "B36": {"content": "=_t(\"< 2: company might be slow to pay suppliers\")"}, "B37": {"content": "=_t(\"possible issue in providers payments, could lead to loss of suppliers trust\")"}, "B38": {"content": "=_t(\"Very dependent on the sector\")"}, "B39": {"content": "=_t(\"Best if compared with competitors\")"}, "D1": {"style": 1}, "D2": {"style": 1, "content": "=_t(\"Net income / Revenue\")"}, "D3": {"content": "=_t(\"< 3%: not efficient at generating business\")"}, "D4": {"content": "=_t(\"> 10%: very efficient\")"}, "D5": {"content": "=_t(\"possible issue in direct and indirect costs\")"}, "D10": {"style": 1, "content": "=_t(\"Total liabilities / Total shareholders’ equity\")"}, "D11": {"content": "=_t(\"< 2.5: mature company who accumulated a lot of money\")"}, "D12": {"content": "=_t(\"> 5: company owns a lot of debt and not of a lot of own money\")"}, "D13": {"content": "=_t(\"possible issue in ressources allocation or missed growth opportunities\")"}, "D18": {"style": 1, "content": "=_t(\"Cash flow / Current liabilities\")"}, "D19": {"content": "=_t(\"> 1: income allow to meet financial obligations\")"}, "D20": {"content": "=_t(\"< 0.8: income might be too low\")"}, "D21": {"content": "=_t(\"number of times you can pay off current debts with cash generated per year\")"}, "D26": {"style": 1, "content": "=_t(\"Quick assets / Current liabilities\")"}, "D27": {"content": "=_t(\"> 1: company in highly solvent position\")"}, "D28": {"content": "=_t(\"< 0.7: company might be stuck with non liquid assets\")"}, "D29": {"content": "=_t(\"possible issues in cash availability at short term\")"}, "D34": {"style": 1, "content": "=_t(\"Sales on account / Average accounts receivable balance for period\")"}, "D35": {"content": "=_t(\"> 10: company gets paid for sales quickly\")"}, "D36": {"content": "=_t(\"< 6: company might not get paid for sales quickly enough\")"}, "D37": {"content": "=_t(\"possible issue in payment terms agreement with clients to get paid faster\")"}, "D38": {"content": "=_t(\"Very dependent on the sector\")"}, "D39": {"content": "=_t(\"Best if compared with competitors\")"}}, "conditionalFormats": [], "figures": [{"id": "3d4e431e-fdfb-4aba-baba-14a9990b0a22", "x": 0, "y": 0, "width": 225, "height": 184, "tag": "chart", "data": {"sectionRule": {"colors": {"lowerColor": "#cc0000", "middleColor": "#f1c232", "upperColor": "#6aa84f"}, "rangeMin": "0", "rangeMax": "1", "lowerInflectionPoint": {"type": "number", "value": "0.2"}, "upperInflectionPoint": {"type": "number", "value": "0.5"}}, "title": "Gross profit margin", "type": "gauge", "dataRange": "Data!B2"}}, {"id": "777d58a8-e76e-4ef6-a16a-8ded6a18a524", "x": 670, "y": 0, "width": 225, "height": 184, "tag": "chart", "data": {"sectionRule": {"colors": {"lowerColor": "#cc0000", "middleColor": "#f1c232", "upperColor": "#6aa84f"}, "rangeMin": "0", "rangeMax": "0.5", "lowerInflectionPoint": {"type": "number", "value": "0.03"}, "upperInflectionPoint": {"type": "number", "value": "0.06"}}, "title": "Net profit margin", "type": "gauge", "dataRange": "Data!B3"}}, {"id": "7c87cbda-1869-4ada-a479-f83447de6c35", "x": 0, "y": 184, "width": 225, "height": 184, "tag": "chart", "data": {"sectionRule": {"colors": {"lowerColor": "#cc0000", "middleColor": "#f1c232", "upperColor": "#6aa84f"}, "rangeMin": "0", "rangeMax": "0.5", "lowerInflectionPoint": {"type": "number", "value": "0.05"}, "upperInflectionPoint": {"type": "number", "value": "0.1"}}, "title": "Operating margin", "type": "gauge", "dataRange": "Data!B4"}}, {"id": "da7d6686-f16e-4ecc-9e4f-6e855e2472b1", "x": 0, "y": 368, "width": 225, "height": 184, "tag": "chart", "data": {"sectionRule": {"colors": {"lowerColor": "#cc0000", "middleColor": "#f1c232", "upperColor": "#6aa84f"}, "rangeMin": "0", "rangeMax": "10", "lowerInflectionPoint": {"type": "number", "value": "1"}, "upperInflectionPoint": {"type": "number", "value": "1.5"}}, "title": "Current ratio", "type": "gauge", "dataRange": "Data!B6"}}, {"id": "dd3e9285-4be0-4511-b6e9-c1d6b27aa6fa", "x": 670, "y": 368, "width": 225, "height": 184, "tag": "chart", "data": {"sectionRule": {"colors": {"lowerColor": "#cc0000", "middleColor": "#f1c232", "upperColor": "#6aa84f"}, "rangeMin": "-2", "rangeMax": "12", "lowerInflectionPoint": {"type": "number", "value": "0.8"}, "upperInflectionPoint": {"type": "number", "value": "1"}}, "title": "Cash flow ratio", "type": "gauge", "dataRange": "Data!B7"}}, {"id": "ce3a4fe4-5bd7-4571-942e-913061864e42", "x": 0, "y": 552, "width": 225, "height": 184, "tag": "chart", "data": {"sectionRule": {"colors": {"lowerColor": "#cc0000", "middleColor": "#f1c232", "upperColor": "#6aa84f"}, "rangeMin": "-1000", "rangeMax": "1000", "lowerInflectionPoint": {"type": "number", "value": "0"}, "upperInflectionPoint": {"type": "number", "value": "0"}}, "title": "Working capital", "type": "gauge", "dataRange": "Data!B8"}}, {"id": "262de936-0aa6-42f5-9c2a-94ca9f3fd6f9", "x": 670, "y": 552, "width": 225, "height": 184, "tag": "chart", "data": {"sectionRule": {"colors": {"lowerColor": "#cc0000", "middleColor": "#f1c232", "upperColor": "#6aa84f"}, "rangeMin": "0", "rangeMax": "5", "lowerInflectionPoint": {"type": "number", "value": "0.7"}, "upperInflectionPoint": {"type": "number", "value": "1"}}, "title": "Quick ratio", "type": "gauge", "dataRange": "Data!B9"}}, {"id": "244bf3e6-9fb6-4979-b01c-303b7737bd7f", "x": 0, "y": 736, "width": 225, "height": 184, "tag": "chart", "data": {"sectionRule": {"colors": {"lowerColor": "#cc0000", "middleColor": "#f1c232", "upperColor": "#6aa84f"}, "rangeMin": "0", "rangeMax": "20", "lowerInflectionPoint": {"type": "number", "value": "2"}, "upperInflectionPoint": {"type": "number", "value": "3"}}, "title": "Payable turnover", "type": "gauge", "dataRange": "Data!B10"}}, {"id": "19747691-3f23-4572-bda0-4090744d9d13", "x": 670, "y": 736, "width": 225, "height": 184, "tag": "chart", "data": {"sectionRule": {"colors": {"lowerColor": "#cc0000", "middleColor": "#f1c232", "upperColor": "#6aa84f"}, "rangeMin": "0", "rangeMax": "20", "lowerInflectionPoint": {"type": "number", "value": "6"}, "upperInflectionPoint": {"type": "number", "value": "10"}}, "title": "Receivable turnover", "type": "gauge", "dataRange": "Data!B11"}}, {"id": "c73f1479-08b5-4598-86b3-90ae40a81528", "x": 670, "y": 184, "width": 225, "height": 184, "tag": "chart", "data": {"sectionRule": {"colors": {"lowerColor": "#6aa84f", "middleColor": "#f1c232", "upperColor": "#cc0000"}, "rangeMin": "0", "rangeMax": "10", "lowerInflectionPoint": {"type": "number", "value": "2.5"}, "upperInflectionPoint": {"type": "number", "value": "5"}}, "title": "Debt-to-equity", "type": "gauge", "dataRange": "Data!B5"}}], "areGridLinesVisible": true, "isVisible": true}, {"id": "15fc34c2-8752-4a36-9ecf-84aa216ecf2c", "name": "Data", "colNumber": 30, "rowNumber": 93, "rows": {"3": {"size": 23}, "4": {"size": 23}, "22": {"size": 23}, "23": {"size": 23}, "24": {"size": 23}}, "cols": {"0": {"size": 280.193359375}, "1": {"size": 175.193359375}, "2": {"size": 175.193359375}, "3": {"size": 175.193359375}, "4": {"size": 175.193359375}, "5": {"size": 119}}, "merges": [], "cells": {"A1": {"style": 1, "content": "=_t(\"KPI\")"}, "A2": {"content": "=_t(\"Gross profit margin\")"}, "A3": {"content": "=_t(\"Net profit margin\")"}, "A4": {"content": "=_t(\"Operating margin\")"}, "A5": {"content": "=_t(\"Debt-to-equity\")"}, "A6": {"content": "=_t(\"Current ratio\")"}, "A7": {"style": 2, "content": "=_t(\"Cash flow ratio\")"}, "A8": {"content": "=_t(\"Working capital\")"}, "A9": {"content": "=_t(\"Quick ratio\")"}, "A10": {"content": "=_t(\"Average debtor days\")"}, "A11": {"content": "=_t(\"Average creditors days\")"}, "A13": {"style": 1, "content": "=_t(\"Aggregate\")"}, "A14": {"style": 3, "content": "=_t(\"Net sales\")"}, "A15": {"style": 3, "content": "=_t(\"COGS\")"}, "A16": {"style": 4, "content": "=_t(\"Net income\")"}, "A17": {"style": 3, "content": "=_t(\"Total income\")"}, "A18": {"style": 3, "content": "=_t(\"Total expense\")"}, "A19": {"style": 4, "content": "=_t(\"Revenue\")"}, "A20": {"style": 3, "content": "=_t(\"EBIT\")"}, "A21": {"style": 3, "content": "=_t(\"Current assets\")"}, "A22": {"style": 3, "content": "=_t(\"Current liabilities\")"}, "A23": {"style": 3, "content": "=_t(\"Total assets\")"}, "A24": {"style": 3, "content": "=_t(\"Total liabilities\")"}, "A25": {"style": 4, "content": "=_t(\"Total shareholder's equity\")"}, "A26": {"style": 3, "content": "=_t(\"Cash flow\")"}, "A27": {"style": 3, "content": "=_t(\"Quick assets\")"}, "A28": {"style": 3, "content": "=_t(\"Payables\")"}, "A29": {"style": 3, "content": "=_t(\"Receivables\")"}, "A31": {"style": 5, "content": "=_t(\"Account type\")"}, "A32": {"style": 6, "content": "=_t(\"Receivable\")"}, "A33": {"style": 6, "content": "=_t(\"Bank and Cash\")"}, "A34": {"style": 6, "content": "=_t(\"Current Assets\")"}, "A35": {"style": 6, "content": "=_t(\"Non-current Assets\")"}, "A36": {"style": 6, "content": "=_t(\"Prepayments\")"}, "A37": {"style": 6, "content": "=_t(\"Fixed Assets\")"}, "A38": {"style": 6, "content": "=_t(\"Payable\")"}, "A39": {"style": 6, "content": "=_t(\"Credit Card\")"}, "A40": {"style": 6, "content": "=_t(\"Current Liabilities\")"}, "A41": {"style": 6, "content": "=_t(\"Non-current Liabilities\")"}, "A42": {"style": 6, "content": "=_t(\"Equity\")"}, "A43": {"style": 6, "content": "=_t(\"Current Year Earnings\")"}, "A44": {"style": 7, "content": "=_t(\"Income\")"}, "A45": {"style": 7, "content": "=_t(\"Other Income\")"}, "A46": {"style": 7, "content": "=_t(\"Expenses\")"}, "A47": {"style": 7, "content": "=_t(\"Depreciation\")"}, "A48": {"style": 7, "content": "=_t(\"Cost of Revenue\")"}, "A49": {"style": 8, "content": "=_t(\"Off-Balance Sheet\")"}, "B1": {"style": 1, "format": 1, "content": "=_t(\"Value\")"}, "B2": {"format": 2, "content": "=IFERROR((B14-B15)/B14)"}, "B3": {"format": 2, "content": "=IFERROR(B16/B19)"}, "B4": {"format": 2, "content": "=IFERROR(B20/B14)"}, "B5": {"format": 3, "content": "=IFERROR(B24/B25)"}, "B6": {"format": 3, "content": "=IFERROR(B21/B22)"}, "B7": {"format": 3, "content": "=IFERROR(B26/B22)"}, "B8": {"format": 3, "content": "=FORMAT.LARGE.NUMBER(IFERROR(B21-B22))"}, "B9": {"format": 3, "content": "=IFERROR(B27/B22)"}, "B10": {"style": 2, "format": 3, "content": "=IFERROR(B28/B18*120,0)"}, "B11": {"style": 2, "format": 3, "content": "=IFERROR(B29/B17*120)"}, "B13": {"style": 9, "content": "=_t(\"Value\")"}, "B14": {"style": 3, "content": "=B44"}, "B15": {"style": 3, "content": "=B48"}, "B16": {"style": 4, "content": "=B14-B15"}, "B17": {"style": 3, "content": "=B44+B45"}, "B18": {"style": 3, "content": "=B46+B47+B48"}, "B19": {"style": 4, "content": "=B17-B18"}, "B20": {"style": 3, "content": "=B44-B46"}, "B21": {"style": 3, "content": "=B34"}, "B22": {"style": 3, "content": "=B40"}, "B23": {"style": 3, "content": "=B32+B33+B34+B35+B36+B37"}, "B24": {"style": 3, "content": "=B38+B39+B40+B41"}, "B25": {"style": 4, "content": "=B23-B24"}, "B26": {"style": 3, "content": "=B33"}, "B27": {"style": 3, "content": "=B32+B33+B34"}, "B28": {"style": 3, "content": "=B38"}, "B29": {"style": 3, "content": "=B32"}, "B31": {"style": 10, "content": "=_t(\"Delta\")"}, "B32": {"style": 6, "content": "=C32-D32"}, "B33": {"style": 6, "content": "=C33-D33"}, "B34": {"style": 6, "content": "=C34-D34"}, "B35": {"style": 6, "content": "=C35-D35"}, "B36": {"style": 6, "content": "=C36-D36"}, "B37": {"style": 6, "content": "=C37-D37"}, "B38": {"style": 6, "content": "=C38-D38"}, "B39": {"style": 6, "content": "=C39-D39"}, "B40": {"style": 6, "content": "=C40-D40"}, "B41": {"style": 6, "content": "=C41-D41"}, "B42": {"style": 6, "content": "=C42-D42"}, "B43": {"style": 6, "content": "=C43-D43"}, "B44": {"style": 7, "content": "=C44-D44"}, "B45": {"style": 7, "content": "=C45-D45"}, "B46": {"style": 7, "content": "=C46-D46"}, "B47": {"style": 7, "content": "=C47-D47"}, "B48": {"style": 7, "content": "=C48-D48"}, "B49": {"style": 8, "content": "=C49-D49"}, "C1": {"style": 1, "content": "=_t(\"Formula\")"}, "C2": {"content": "=_t(\"(Net sales – COGS) / Net sales\")"}, "C3": {"content": "=_t(\"Net income / Revenue\")"}, "C4": {"content": "=_t(\"EBIT / Net sales\")"}, "C5": {"content": "=_t(\"Total liabilities / Total shareholders’ equity\")"}, "C6": {"content": "=_t(\"Current assets / Current liabilities\")"}, "C7": {"content": "=_t(\"Cash flow / Current liabilities\")"}, "C8": {"content": "=_t(\"Current assets – Current liabilities\")"}, "C9": {"content": "=_t(\"Quick assets / Current liabilities\")"}, "C10": {"content": "=_t(\"Payables / Expenses * 120\")"}, "C11": {"content": "=_t(\"Receivables / Income * 120\")"}, "C14": {"style": 2, "content": "=_t(\"income\")"}, "C15": {"style": 2, "content": "=_t(\"cost of revenue\")"}, "C16": {"style": 2, "content": "=_t(\"net sales - COGS\")"}, "C17": {"style": 2, "content": "=_t(\"income + other income\")"}, "C18": {"style": 2, "content": "=_t(\"expenses + depreciation + cost of revenue\")"}, "C19": {"style": 2, "content": "=_t(\"total income - total expense\")"}, "C20": {"style": 2, "content": "=_t(\"expenses - income\")"}, "C21": {"style": 2, "content": "=_t(\"current assets\")"}, "C22": {"style": 2, "content": "=_t(\"current liabilities\")"}, "C23": {"style": 2, "content": "=_t(\"receivable + bank and cash + current assets + non-current assets + prepayments + fixed assets\")"}, "C24": {"style": 2, "content": "=_t(\"payable + credit card + current liabilities + non-current liabilities\")"}, "C25": {"style": 2, "content": "=_t(\"total assets - total liabilities\")"}, "C26": {"style": 2, "content": "=_t(\"bank and cash\")"}, "C27": {"style": 2, "content": "=_t(\"receivable + bank and cash + current assets\")"}, "C28": {"style": 2, "content": "=_t(\"payable\")"}, "C29": {"style": 2, "content": "=_t(\"receivable\")"}, "C31": {"style": 10, "content": "=F8"}, "C32": {"style": 6, "content": "=ODOO.BALANCE(ODOO.ACCOUNT.GROUP($E32),C$31)"}, "C33": {"style": 6, "content": "=ODOO.BALANCE(ODOO.ACCOUNT.GROUP($E33),C$31)"}, "C34": {"style": 6, "content": "=ODOO.BALANCE(ODOO.ACCOUNT.GROUP($E34),C$31)"}, "C35": {"style": 6, "content": "=ODOO.BALANCE(ODOO.ACCOUNT.GROUP($E35),C$31)"}, "C36": {"style": 6, "content": "=ODOO.BALANCE(ODOO.ACCOUNT.GROUP($E36),C$31)"}, "C37": {"style": 6, "content": "=ODOO.BALANCE(ODOO.ACCOUNT.GROUP($E37),C$31)"}, "C38": {"style": 6, "content": "=ODOO.BALANCE(ODOO.ACCOUNT.GROUP($E38),C$31)"}, "C39": {"style": 6, "content": "=ODOO.BALANCE(ODOO.ACCOUNT.GROUP($E39),C$31)"}, "C40": {"style": 6, "content": "=ODOO.BALANCE(ODOO.ACCOUNT.GROUP($E40),C$31)"}, "C41": {"style": 6, "content": "=ODOO.BALANCE(ODOO.ACCOUNT.GROUP($E41),C$31)"}, "C42": {"style": 6, "content": "=ODOO.BALANCE(ODOO.ACCOUNT.GROUP($E42),C$31)"}, "C43": {"style": 6, "content": "=ODOO.BALANCE(ODOO.ACCOUNT.GROUP($E43),C$31)"}, "C44": {"style": 7, "content": "=ODOO.BALANCE(ODOO.ACCOUNT.GROUP($E44),C$31)"}, "C45": {"style": 7, "content": "=ODOO.BALANCE(ODOO.ACCOUNT.GROUP($E45),C$31)"}, "C46": {"style": 7, "content": "=ODOO.BALANCE(ODOO.ACCOUNT.GROUP($E46),C$31)"}, "C47": {"style": 7, "content": "=ODOO.BALANCE(ODOO.ACCOUNT.GROUP($E47),C$31)"}, "C48": {"style": 7, "content": "=ODOO.BALANCE(ODOO.ACCOUNT.GROUP($E48),C$31)"}, "C49": {"style": 8, "content": "=ODOO.BALANCE(ODOO.ACCOUNT.GROUP($E49),C$31)"}, "D14": {"style": 2}, "D15": {"style": 2}, "D16": {"style": 2}, "D17": {"style": 2}, "D18": {"style": 2}, "D19": {"style": 2}, "D20": {"style": 2}, "D21": {"style": 2}, "D22": {"style": 2}, "D23": {"style": 2}, "D24": {"style": 2}, "D25": {"style": 2}, "D26": {"style": 2}, "D27": {"style": 2}, "D28": {"style": 2}, "D29": {"style": 2}, "D31": {"style": 10, "content": "=F12"}, "D32": {"style": 6, "content": "=ODOO.BALANCE(ODOO.ACCOUNT.GROUP($E32),D$31)"}, "D33": {"style": 6, "content": "=ODOO.BALANCE(ODOO.ACCOUNT.GROUP($E33),D$31)"}, "D34": {"style": 6, "content": "=ODOO.BALANCE(ODOO.ACCOUNT.GROUP($E34),D$31)"}, "D35": {"style": 6, "content": "=ODOO.BALANCE(ODOO.ACCOUNT.GROUP($E35),D$31)"}, "D36": {"style": 6, "content": "=ODOO.BALANCE(ODOO.ACCOUNT.GROUP($E36),D$31)"}, "D37": {"style": 6, "content": "=ODOO.BALANCE(ODOO.ACCOUNT.GROUP($E37),D$31)"}, "D38": {"style": 6, "content": "=ODOO.BALANCE(ODOO.ACCOUNT.GROUP($E38),D$31)"}, "D39": {"style": 6, "content": "=ODOO.BALANCE(ODOO.ACCOUNT.GROUP($E39),D$31)"}, "D40": {"style": 6, "content": "=ODOO.BALANCE(ODOO.ACCOUNT.GROUP($E40),D$31)"}, "D41": {"style": 6, "content": "=ODOO.BALANCE(ODOO.ACCOUNT.GROUP($E41),D$31)"}, "D42": {"style": 6, "content": "=ODOO.BALANCE(ODOO.ACCOUNT.GROUP($E42),D$31)"}, "D43": {"style": 6, "content": "=ODOO.BALANCE(ODOO.ACCOUNT.GROUP($E43),D$31)"}, "D44": {"style": 7, "content": "=ODOO.BALANCE(ODOO.ACCOUNT.GROUP($E44),D$31)"}, "D45": {"style": 7, "content": "=ODOO.BALANCE(ODOO.ACCOUNT.GROUP($E45),D$31)"}, "D46": {"style": 7, "content": "=ODOO.BALANCE(ODOO.ACCOUNT.GROUP($E46),D$31)"}, "D47": {"style": 7, "content": "=ODOO.BALANCE(ODOO.ACCOUNT.GROUP($E47),D$31)"}, "D48": {"style": 7, "content": "=ODOO.BALANCE(ODOO.ACCOUNT.GROUP($E48),D$31)"}, "D49": {"style": 8, "content": "=ODOO.BALANCE(ODOO.ACCOUNT.GROUP($E49),D$31)"}, "E1": {"style": 11, "content": "=_t(\"Month-Year\")"}, "E2": {"style": 1, "content": "=_t(\"Month\")"}, "E3": {"style": 1, "content": "=_t(\"Year\")"}, "E4": {"style": 1, "content": "=_t(\"Start date\")"}, "E5": {"style": 1, "content": "=_t(\"Offset -1 - Start date\")"}, "E6": {"style": 1, "content": "=_t(\"Offset -1 - Month\")"}, "E7": {"style": 1, "content": "=_t(\"Offset -1 - Year\")"}, "E8": {"style": 11, "content": "=_t(\"Offset -1 - Month-Year\")"}, "E9": {"style": 1, "content": "=_t(\"Offset -4 - Start date\")"}, "E10": {"style": 1, "content": "=_t(\"Offset -4 - Month\")"}, "E11": {"style": 1, "content": "=_t(\"Offset -4 - Year\")"}, "E12": {"style": 11, "content": "=_t(\"Offset -4 - Month-Year\")"}, "E31": {"style": 5, "content": "=_t(\"Technical name\")"}, "E32": {"style": 6, "content": "asset_receivable"}, "E33": {"style": 6, "content": "asset_cash"}, "E34": {"style": 6, "content": "asset_current"}, "E35": {"style": 6, "content": "asset_non_current"}, "E36": {"style": 6, "content": "asset_prepayments"}, "E37": {"style": 6, "content": "asset_fixed"}, "E38": {"style": 6, "content": "liability_payable"}, "E39": {"style": 6, "content": "liability_credit_card"}, "E40": {"style": 6, "content": "liability_current"}, "E41": {"style": 6, "content": "liability_non_current"}, "E42": {"style": 6, "content": "equity"}, "E43": {"style": 6, "content": "equity_unaffected"}, "E44": {"style": 7, "content": "income"}, "E45": {"style": 7, "content": "income_other"}, "E46": {"style": 7, "content": "expense"}, "E47": {"style": 7, "content": "expense_depreciation"}, "E48": {"style": 7, "content": "expense_direct_cost"}, "E49": {"style": 8, "content": "off_balance"}, "F1": {"style": 12, "content": "=ODOO.FILTER.VALUE(\"Month\")"}, "F2": {"style": 13, "content": "=LEFT(F1,2)"}, "F3": {"style": 13, "content": "=RIGHT(F1,4)"}, "F4": {"style": 13, "content": "=CONCATENATE(F2,\"/01/\",F3)"}, "F5": {"style": 13, "format": 4, "content": "=EDATE(F4,-1)"}, "F6": {"style": 13, "content": "=IF(LEN(MONTH(F5))=1,CONCATENATE(\"0\",MONTH(F5)),MONTH(F5))"}, "F7": {"style": 13, "content": "=YEAR(F5)"}, "F8": {"style": 12, "format": 4, "content": "=CONCATENATE(F6,\"/\",F7)"}, "F9": {"style": 13, "content": "=EDATE(F4,-4)"}, "F10": {"style": 13, "content": "=IF(LEN(MONTH(F9))=1,CONCATENATE(\"0\",MONTH(F9)),MONTH(F9))"}, "F11": {"style": 13, "content": "=YEAR(F9)"}, "F12": {"style": 12, "content": "=CONCATENATE(F10,\"/\",F11)"}, "J36": {"format": 4}, "K36": {"format": 4}}, "conditionalFormats": [], "figures": [], "areGridLinesVisible": true, "isVisible": true}], "entities": {}, "styles": {"1": {"bold": true}, "2": {"fillColor": ""}, "3": {"fillColor": "#d9ead3"}, "4": {"fillColor": "#b6d7a8"}, "5": {"fillColor": "#a4c2f4", "bold": true}, "6": {"fillColor": "#c9daf8"}, "7": {"fillColor": "#ead1dc"}, "8": {"fillColor": "#f3f3f3"}, "9": {"align": "right", "bold": true}, "10": {"fillColor": "#a4c2f4", "bold": true, "align": "right"}, "11": {"bold": true, "fillColor": "#c9daf8"}, "12": {"bold": false, "align": "left", "fillColor": "#c9daf8"}, "13": {"bold": false, "align": "left"}}, "formats": {"1": "0%", "2": "0.0%", "3": "0.0", "4": "m/d/yyyy"}, "borders": {}, "revisionId": "START_REVISION", "chartOdooMenusReferences": {}, "odooVersion": 3, "pivots": {}, "pivotNextId": 1, "lists": {}, "listNextId": 1, "globalFilters": [{"id": "069d7cf1-9623-4d6e-8226-ad7c60112662", "type": "date", "label": "Month", "defaultValue": {}, "rangeType": "month", "defaultsToCurrentPeriod": true, "pivotFields": {}, "listFields": {}, "graphFields": {}}]}