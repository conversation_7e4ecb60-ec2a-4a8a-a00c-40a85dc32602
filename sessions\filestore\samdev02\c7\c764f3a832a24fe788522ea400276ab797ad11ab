)]}'
{"version": 3, "sources": ["/web/static/lib/bootstrap/scss/_functions.scss", "/web/static/lib/bootstrap/scss/_mixins.scss", "/web/static/src/scss/mixins_forwardport.scss", "/web/static/src/scss/bs_mixins_overrides.scss", "/web/static/src/legacy/scss/utils.scss", "/web_enterprise/static/src/scss/primary_variables.scss", "/web/static/src/scss/primary_variables.scss", "/web_enterprise/static/src/core/notifications/notifications.variables.scss", "/web_enterprise/static/src/webclient/home_menu/home_menu.variables.scss", "/web_enterprise/static/src/webclient/navbar/navbar.variables.scss", "/web/static/src/core/notifications/notification.variables.scss", "/web/static/src/search/control_panel/control_panel.variables.scss", "/web/static/src/search/search_panel/search_panel.variables.scss", "/web/static/src/views/form/form.variables.scss", "/web/static/src/views/kanban/kanban.variables.scss", "/web/static/src/webclient/burger_menu/burger_menu.variables.scss", "/web/static/src/webclient/navbar/navbar.variables.scss", "/base/static/src/scss/onboarding.variables.scss", "/mail/static/src/scss/variables/primary_variables.scss", "/web_editor/static/src/scss/web_editor.variables.scss", "/web_editor/static/src/scss/wysiwyg.variables.scss", "/portal/static/src/scss/primary_variables.scss", "/account/static/src/scss/variables.scss", "/website/static/src/scss/primary_variables.scss", "/website/static/src/scss/options/user_values.scss", "/website/static/src/scss/options/colors/user_color_palette.scss", "/website/static/src/scss/options/colors/user_gray_color_palette.scss", "/website/static/src/scss/options/colors/user_theme_color_palette.scss", "/web_gantt/static/src/scss/web_gantt.variables.scss", "/documents/static/src/scss/documents.variables.scss", "/hr_org_chart/static/src/scss/variables.scss", "/website/static/src/snippets/s_badge/000_variables.scss", "/website/static/src/snippets/s_product_list/000_variables.scss", "/website/static/src/scss/secondary_variables.scss", "/web_enterprise/static/src/scss/secondary_variables.scss", "/web/static/src/scss/secondary_variables.scss", "/web_editor/static/src/scss/secondary_variables.scss", "/web_editor/static/src/scss/bootstrap_overridden.scss", "/web/static/src/scss/pre_variables.scss", "/web/static/lib/bootstrap/scss/_variables.scss", "/website/static/src/scss/website.wysiwyg.scss", "/website/static/src/scss/website.edit_mode.scss", "/website_livechat/static/src/scss/website_livechat.edit_mode.scss"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACt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rPA;AACA;AACA;AACA;AACA;AACA", "sourcesContent": ["\n/* /web/static/lib/bootstrap/scss/_functions.scss */\n\n", "\n/* /web/static/lib/bootstrap/scss/_mixins.scss */\n\n", "\n/* /web/static/src/scss/mixins_forwardport.scss */\n\n", "\n/* /web/static/src/scss/bs_mixins_overrides.scss */\n\n", "\n/* /web/static/src/legacy/scss/utils.scss */\n\n", "\n/* /web_enterprise/static/src/scss/primary_variables.scss */\n\n", "\n/* /web/static/src/scss/primary_variables.scss */\n\n", "\n/* /web_enterprise/static/src/core/notifications/notifications.variables.scss */\n\n", "\n/* /web_enterprise/static/src/webclient/home_menu/home_menu.variables.scss */\n\n", "\n/* /web_enterprise/static/src/webclient/navbar/navbar.variables.scss */\n\n", "\n/* /web/static/src/core/notifications/notification.variables.scss */\n\n", "\n/* /web/static/src/search/control_panel/control_panel.variables.scss */\n\n", "\n/* /web/static/src/search/search_panel/search_panel.variables.scss */\n\n", "\n/* /web/static/src/views/form/form.variables.scss */\n\n", "\n/* /web/static/src/views/kanban/kanban.variables.scss */\n\n", "\n/* /web/static/src/webclient/burger_menu/burger_menu.variables.scss */\n\n", "\n/* /web/static/src/webclient/navbar/navbar.variables.scss */\n\n", "\n/* /base/static/src/scss/onboarding.variables.scss */\n\n", "\n/* /mail/static/src/scss/variables/primary_variables.scss */\n\n", "\n/* /web_editor/static/src/scss/web_editor.variables.scss */\n\n", "\n/* /web_editor/static/src/scss/wysiwyg.variables.scss */\n\n", "\n/* /portal/static/src/scss/primary_variables.scss */\n\n", "\n/* /account/static/src/scss/variables.scss */\n\n@import url(\"https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i&display=swap\");\n@import url(\"https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,700,700i&display=swap\");\n@import url(\"https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,300i,400,400i,700,700i&display=swap\");\n@import url(\"https://fonts.googleapis.com/css?family=Raleway:300,300i,400,400i,700,700i&display=swap\");\n@import url(\"https://fonts.googleapis.com/css?family=Noto+Serif:300,300i,400,400i,700,700i&display=swap\");\n@import url(\"https://fonts.googleapis.com/css?family=Arvo:300,300i,400,400i,700,700i&display=swap\");\n@keyframes animate-red {\n  0% {\n    color: red;\n  }\n  100% {\n    color: inherit;\n  }\n}\n\n.animate {\n  animation: animate-red 1s ease;\n}\n\n", "\n/* /website/static/src/scss/primary_variables.scss */\n\n", "\n/* /website/static/src/scss/options/user_values.scss */\n\n", "\n/* /website/static/src/scss/options/colors/user_color_palette.scss */\n\n", "\n/* /website/static/src/scss/options/colors/user_gray_color_palette.scss */\n\n", "\n/* /website/static/src/scss/options/colors/user_theme_color_palette.scss */\n\n", "\n/* /web_gantt/static/src/scss/web_gantt.variables.scss */\n\n", "\n/* /documents/static/src/scss/documents.variables.scss */\n\n", "\n/* /hr_org_chart/static/src/scss/variables.scss */\n\n", "\n/* /website/static/src/snippets/s_badge/000_variables.scss */\n\n", "\n/* /website/static/src/snippets/s_product_list/000_variables.scss */\n\n", "\n/* /website/static/src/scss/secondary_variables.scss */\n\n", "\n/* /web_enterprise/static/src/scss/secondary_variables.scss */\n\n", "\n/* /web/static/src/scss/secondary_variables.scss */\n\n", "\n/* /web_editor/static/src/scss/secondary_variables.scss */\n\n", "\n/* /web_editor/static/src/scss/bootstrap_overridden.scss */\n\n", "\n/* /web/static/src/scss/pre_variables.scss */\n\n", "\n/* /web/static/lib/bootstrap/scss/_variables.scss */\n\n", "\n/* /website/static/src/scss/website.wysiwyg.scss */\n\n#oe_snippets {\n  top: 0;\n}\n\n#oe_snippets .oe-toolbar .color-indicator {\n  padding: 0 2px 2px 2px;\n}\n\nhtml[lang] > body.editor_enable [data-oe-translation-state] {\n  background: rgba(255, 255, 90, 0.5) !important;\n}\n\nhtml[lang] > body.editor_enable [data-oe-translation-state][data-oe-translation-state=\"translated\"] {\n  background: rgba(120, 215, 110, 0.5) !important;\n}\n\nhtml[lang] > body.editor_enable [data-oe-translation-state].o_dirty {\n  background: rgba(120, 215, 110, 0.25) !important;\n}\n\nhtml[data-edit_translations=\"1\"] .o_translate_mode_hidden {\n  display: none !important;\n}\n\n.o_snippet_override_invisible {\n  display: block !important;\n  opacity: 70%;\n  position: relative;\n}\n\n.o_snippet_override_invisible::before {\n  position: absolute;\n  width: -webkit-fill-available;\n  width: -moz-available;\n  right: 20px;\n  z-index: 100;\n  background-color: #01bad2;\n  font-size: 0px;\n  content: \".\";\n}\n\n.o_snippet_override_invisible.d-md-none::before, .o_snippet_override_invisible.d-lg-none::before {\n  height: 50px;\n  -webkit-mask: url(\"/website/static/src/img/snippets_options/desktop_invisible.svg\") no-repeat 100% 100%;\n}\n\n.o_snippet_override_invisible:not(.d-md-none):not(.d-lg-none)::before {\n  height: 30px;\n  -webkit-mask: url(\"/website/static/src/img/snippets_options/mobile_invisible.svg\") no-repeat 100% 100%;\n}\n\nwe-toggler.o_we_option_font_1, we-button.o_we_option_font_1 > div {\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Ubuntu, \"Liberation Sans\", Arial, \"Odoo Unicode Support Noto\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n}\n\nwe-toggler.o_we_option_font_2, we-button.o_we_option_font_2 > div {\n  font-family: \"Roboto\", \"Odoo Unicode Support Noto\", sans-serif;\n}\n\nwe-toggler.o_we_option_font_3, we-button.o_we_option_font_3 > div {\n  font-family: \"Open Sans\", \"Odoo Unicode Support Noto\", sans-serif;\n}\n\nwe-toggler.o_we_option_font_4, we-button.o_we_option_font_4 > div {\n  font-family: \"Source Sans Pro\", \"Odoo Unicode Support Noto\", sans-serif;\n}\n\nwe-toggler.o_we_option_font_5, we-button.o_we_option_font_5 > div {\n  font-family: \"Raleway\", \"Odoo Unicode Support Noto\", sans-serif;\n}\n\nwe-toggler.o_we_option_font_6, we-button.o_we_option_font_6 > div {\n  font-family: \"Noto Serif\", \"Odoo Unicode Support Noto\", serif;\n}\n\nwe-toggler.o_we_option_font_7, we-button.o_we_option_font_7 > div {\n  font-family: \"Arvo\", Times, \"Odoo Unicode Support Noto\", serif;\n}\n\n.o_we_add_google_font_btn {\n  border-top: 1px solid currentColor !important;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_user_value_widget.o_palette_color_preview_button {\n  display: -webkit-inline-box; display: -webkit-inline-flex; display: inline-flex;\n  margin: 1% 0;\n  padding-right: 0.3rem;\n  width: 50%;\n  background: transparent;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_user_value_widget.o_palette_color_preview_button.active, #oe_snippets > .o_we_customize_panel .o_we_user_value_widget.o_palette_color_preview_button:hover {\n  background: transparent;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_user_value_widget.o_palette_color_preview_button.active > div, #oe_snippets > .o_we_customize_panel .o_we_user_value_widget.o_palette_color_preview_button:hover > div {\n  box-shadow: 0 0 0 2px #000000, 0 0 0 3px #01bad2;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_user_value_widget.o_palette_color_preview_button > div {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;\n  align-items: stretch;\n  -webkit-box-pack: end; justify-content: flex-end;\n  margin: 3px;\n  min-height: 30px;\n  border-radius: 60px;\n  box-shadow: 0 0 0 1px #000000;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_user_value_widget.o_palette_color_preview_button .o_palette_color_preview {\n  -webkit-box-flex: 1; -webkit-flex: 1 0 0; flex: 1 0 0;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_scroll_effects_selector we-button {\n  padding-top: 8px;\n  padding-bottom: 8px;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_scroll_effects_selector we-button img {\n  max-height: 80px;\n  width: auto;\n  margin-right: 8px;\n  margin-left: 4px;\n}\n\n#oe_snippets > .o_we_customize_panel we-button.o_we_device > div {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  align-items: center;\n}\n\n#oe_snippets > .o_we_customize_panel we-button.o_we_device > div svg {\n  width: 12px;\n  fill: #D9D9D9;\n  margin-bottom: 0;\n}\n\n#oe_snippets > .o_we_customize_panel we-button.o_we_device > div svg:hover {\n  fill: #FFFFFF;\n}\n\n#oe_snippets > .o_we_customize_panel we-button.o_we_device.active > div svg {\n  fill: #e6586c;\n}\n\n#oe_snippets > .o_we_customize_panel we-button.o_we_device.active > div svg:hover {\n  fill: #e1374f;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_theme_colors_selector > we-title {\n  display: none;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_theme_colors_selector .o_we_so_color_palette.o_we_user_value_widget + .o_we_so_color_palette {\n  margin-left: 4px;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_theme_colors_selector .o_we_so_color_palette.o_we_user_value_widget + .o_we_so_color_palette:nth-child(4) {\n  margin-left: 24px;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_theme_colors_selector .o_we_so_color_palette.o_we_user_value_widget .o_we_color_preview {\n  width: 26px;\n  height: 26px;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_theme_colors_selector > div, #oe_snippets > .o_we_customize_panel .o_we_theme_colors_selector we-select.o_we_theme_colors_select, #oe_snippets > .o_we_customize_panel .o_we_theme_colors_selector we-toggler {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_theme_colors_selector > div {\n  align-items: stretch;\n  width: 100%;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_theme_colors_selector we-select.o_we_theme_colors_select {\n  -webkit-box-pack: end; justify-content: flex-end;\n  margin-left: auto;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_theme_colors_selector we-select.o_we_theme_colors_select > div, #oe_snippets > .o_we_customize_panel .o_we_theme_colors_selector we-select.o_we_theme_colors_select we-toggler {\n  height: 100%;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_theme_colors_selector we-select.o_we_theme_colors_select we-selection-items {\n  padding-top: 17px;\n  padding-bottom: 17px;\n  background: #42424c;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_theme_colors_selector we-toggler {\n  align-items: center;\n  padding: 0 0.4rem;\n  font-size: 1.5em;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_theme_colors_selector we-toggler:after {\n  content: none;\n}\n\n#oe_snippets > .o_we_customize_panel .o_palette_color_preview_button > div {\n  min-height: 24px;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_cc_preview_wrapper {\n  border: 1px solid;\n  border-color: rgba(255, 255, 255, 0.2) #000000 transparent;\n  box-shadow: 0 1px 0 #000000;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_cc_preview_wrapper + .o_we_collapse_toggler {\n  height: 35px;\n}\n\n.o_we_border_preview {\n  display: inline-block;\n  width: 999px;\n  max-width: 100%;\n  margin-bottom: 2px;\n  border-width: 4px;\n  border-bottom: none !important;\n}\n\n.pac-container {\n  z-index: 1050;\n  width: 260px !important;\n  font-size: 12px;\n  margin-left: -144px;\n  border: 1px solid #000000;\n  border-top: none;\n  border-radius: 2px;\n  overflow: hidden;\n  background-color: #141217;\n  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.5);\n  margin-top: 8px;\n  transform: translate(41px);\n}\n\n.pac-container:after {\n  display: none;\n}\n\n.pac-container .pac-item {\n  display: block;\n  max-width: 100%;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  vertical-align: top;\n  line-height: 34px;\n  color: #D9D9D9;\n  padding: 0 1em 0 2em;\n  border-top: 1px solid #262626;\n  border-radius: 2px;\n  background-color: #595964;\n  color: #D9D9D9;\n  font-size: 12px;\n}\n\n.pac-container .pac-item:hover, .pac-container .pac-item:focus, .pac-container .pac-item.pac-item-selected {\n  background-color: #2b2b33;\n  cursor: pointer;\n}\n\n.pac-container .pac-item .pac-icon-marker {\n  position: absolute;\n  margin-left: -1em;\n}\n\n.pac-container .pac-item .pac-icon-marker::after {\n  content: '\\f041';\n  font-family: FontAwesome;\n}\n\n.pac-container .pac-item .pac-item-query {\n  margin-right: 0.4em;\n  color: inherit;\n}\n\n.o_table_ui {\n  display: none !important;\n}\n\n", "\n/* /website/static/src/scss/website.edit_mode.scss */\n\n.o_editable.oe_structure.oe_empty#wrap:empty, .o_editable[data-oe-type=html]#wrap:empty, .o_editable .oe_structure.oe_empty#wrap:empty, .o_editable.oe_structure.oe_empty > .oe_drop_zone.oe_insert:not(.oe_vertical), .o_editable[data-oe-type=html] > .oe_drop_zone.oe_insert:not(.oe_vertical), .o_editable .oe_structure.oe_empty > .oe_drop_zone.oe_insert:not(.oe_vertical) {\n  border: 2px dashed #999999;\n  text-align: center;\n  color: white;\n}\n\n.o_editable.oe_structure.oe_empty#wrap:empty:before, .o_editable[data-oe-type=html]#wrap:empty:before, .o_editable .oe_structure.oe_empty#wrap:empty:before, .o_editable.oe_structure.oe_empty > .oe_drop_zone.oe_insert:not(.oe_vertical):before, .o_editable[data-oe-type=html] > .oe_drop_zone.oe_insert:not(.oe_vertical):before, .o_editable .oe_structure.oe_empty > .oe_drop_zone.oe_insert:not(.oe_vertical):before {\n  content: attr(data-editor-message);\n  display: block;\n  font-size: 20px;\n}\n\n.o_editable.oe_structure.oe_empty#wrap:not(:empty)[data-editor-message-default]:empty:before, .o_editable[data-oe-type=html]#wrap:not(:empty)[data-editor-message-default]:empty:before, .o_editable .oe_structure.oe_empty#wrap:not(:empty)[data-editor-message-default]:empty:before, .o_editable.oe_structure.oe_empty > .oe_drop_zone.oe_insert:not(:empty)[data-editor-message-default]:not(.oe_vertical):before, .o_editable[data-oe-type=html] > .oe_drop_zone.oe_insert:not(:empty)[data-editor-message-default]:not(.oe_vertical):before, .o_editable .oe_structure.oe_empty > .oe_drop_zone.oe_insert:not(:empty)[data-editor-message-default]:not(.oe_vertical):before, .o_editable.oe_structure.oe_empty#wrap:not(:only-child)[data-editor-message-default]:empty:before, .o_editable[data-oe-type=html]#wrap:not(:only-child)[data-editor-message-default]:empty:before, .o_editable .oe_structure.oe_empty#wrap:not(:only-child)[data-editor-message-default]:empty:before, .o_editable.oe_structure.oe_empty > .oe_drop_zone.oe_insert:not(:only-child)[data-editor-message-default]:not(.oe_vertical):before, .o_editable[data-oe-type=html] > .oe_drop_zone.oe_insert:not(:only-child)[data-editor-message-default]:not(.oe_vertical):before, .o_editable .oe_structure.oe_empty > .oe_drop_zone.oe_insert:not(:only-child)[data-editor-message-default]:not(.oe_vertical):before {\n  content: none;\n}\n\n.o_editable.oe_structure.oe_empty#wrap:empty:after, .o_editable[data-oe-type=html]#wrap:empty:after, .o_editable .oe_structure.oe_empty#wrap:empty:after, .o_editable.oe_structure.oe_empty > .oe_drop_zone.oe_insert:not(.oe_vertical):after, .o_editable[data-oe-type=html] > .oe_drop_zone.oe_insert:not(.oe_vertical):after, .o_editable .oe_structure.oe_empty > .oe_drop_zone.oe_insert:not(.oe_vertical):after {\n  content: attr(data-editor-sub-message);\n  display: block;\n}\n\n.o_we_snippet_area_animation {\n  animation-delay: 999ms;\n}\n\n.o_we_snippet_area_animation::before {\n  animation: inherit;\n  animation-delay: 0ms;\n}\n\n.o_editable {\n  /* Summernote not Support for placeholder text https://github.com/summernote/summernote/issues/581 */\n}\n\n.o_editable:not(:empty):not([data-oe-model=\"ir.ui.view\"]):not([data-oe-type=\"html\"]):not(.o_editable_no_shadow):not([data-oe-type=\"image\"]):hover, .o_editable:not(:empty).o_editable_date_field_linked, .o_editable[data-oe-type]:not([data-oe-model=\"ir.ui.view\"]):not([data-oe-type=\"html\"]):not(.o_editable_no_shadow):not([data-oe-type=\"image\"]):hover, .o_editable[data-oe-type].o_editable_date_field_linked {\n  box-shadow: #714B67 0 0 5px 2px inset;\n}\n\n.o_editable:not(:empty)[data-oe-type=\"image\"]:not(.o_editable_no_shadow):hover, .o_editable[data-oe-type][data-oe-type=\"image\"]:not(.o_editable_no_shadow):hover {\n  position: relative;\n}\n\n.o_editable:not(:empty)[data-oe-type=\"image\"]:not(.o_editable_no_shadow):hover:after, .o_editable[data-oe-type][data-oe-type=\"image\"]:not(.o_editable_no_shadow):hover:after {\n  content: \"\";\n  pointer-events: none;\n  position: absolute;\n  top: 0;\n  left: 0;\n  bottom: 0;\n  right: 0;\n  z-index: 1;\n  box-shadow: #714B67 0 0 5px 2px inset;\n}\n\n.o_editable:focus, .o_editable[data-oe-type] {\n  min-height: 0.8em;\n  min-width: 8px;\n}\n\n.o_editable:focus#o_footer_scrolltop_wrapper, .o_editable[data-oe-type]#o_footer_scrolltop_wrapper {\n  min-height: 0;\n  min-width: 0;\n}\n\n.o_editable.o_is_inline_editable {\n  display: inline-block;\n}\n\n.o_editable .btn, .o_editable.btn {\n  -webkit-user-select: auto;\n  -moz-user-select: auto;\n  -ms-user-select: auto;\n  user-select: auto;\n  cursor: text !important;\n}\n\n.o_editable[placeholder]:empty:not(:focus):before {\n  content: attr(placeholder);\n  opacity: 0.3;\n  pointer-events: none;\n}\n\n.o_editable.oe_structure.oe_empty > .oe_drop_zone.oe_insert:not(.oe_vertical), .o_editable[data-oe-type=html] > .oe_drop_zone.oe_insert:not(.oe_vertical), .o_editable .oe_structure.oe_empty > .oe_drop_zone.oe_insert:not(.oe_vertical) {\n  height: auto;\n  text-shadow: -1px -1px 0 #714B67, 1px -1px 0 #714B67, -1px 1px 0 #714B67, 1px 1px 0 #714B67;\n}\n\n.o_editable.oe_structure.oe_empty > .oe_drop_zone.oe_insert:not(.oe_vertical):only-child, .o_editable[data-oe-type=html] > .oe_drop_zone.oe_insert:not(.oe_vertical):only-child, .o_editable .oe_structure.oe_empty > .oe_drop_zone.oe_insert:not(.oe_vertical):only-child {\n  margin: 20px 2%;\n  width: 96%;\n  padding: 12px 0px;\n}\n\n.o_editable.oe_structure.oe_empty > .oe_drop_zone.oe_insert:not(.oe_vertical):not(:only-child)::before, .o_editable[data-oe-type=html] > .oe_drop_zone.oe_insert:not(.oe_vertical):not(:only-child)::before, .o_editable .oe_structure.oe_empty > .oe_drop_zone.oe_insert:not(.oe_vertical):not(:only-child)::before {\n  font-size: 16px;\n}\n\n.o_editable.oe_structure.oe_empty#wrap:empty, .o_editable[data-oe-type=html]#wrap:empty, .o_editable .oe_structure.oe_empty#wrap:empty {\n  padding: 112px 0px;\n  margin: 20px 2%;\n  color: #999999;\n}\n\n.o_editable.oe_structure.oe_empty#wrap > .oe_drop_zone.oe_insert:not(.oe_vertical):only-child, .o_editable[data-oe-type=html]#wrap > .oe_drop_zone.oe_insert:not(.oe_vertical):only-child, .o_editable .oe_structure.oe_empty#wrap > .oe_drop_zone.oe_insert:not(.oe_vertical):only-child {\n  padding: 112px 0px;\n  color: #999999;\n  text-shadow: none;\n}\n\n.o_editable.oe_structure.oe_empty > p:empty:only-child, .o_editable[data-oe-type=html] > p:empty:only-child, .o_editable .oe_structure.oe_empty > p:empty:only-child {\n  color: #aaa;\n}\n\n.editor_enable [data-oe-readonly]:hover {\n  cursor: default;\n}\n\n.oe_structure_solo > .oe_drop_zone {\n  transform: translateY(10px);\n}\n\n/* Prevent the text contents of draggable elements from being selectable. */\n[draggable] {\n  user-select: none;\n}\n\n.oe_editable:focus,\n.css_editable_hidden,\n.editor_enable .css_editable_mode_hidden {\n  outline: none !important;\n}\n\n.editor_enable .css_non_editable_mode_hidden,\n.o_editable .media_iframe_video .css_editable_mode_display {\n  display: block !important;\n}\n\n.editor_enable [data-oe-type=html].oe_no_empty:empty {\n  height: 16px !important;\n}\n\ntable.editorbar-panel {\n  cursor: pointer;\n  width: 100%;\n}\n\ntable.editorbar-panel td {\n  border: 1px solid #aaa;\n}\n\ntable.editorbar-panel td.selected {\n  background-color: #b1c9d9;\n}\n\n.link-style .dropdown > .btn {\n  min-width: 160px;\n}\n\n.link-style .link-style {\n  display: none;\n}\n\n.link-style li {\n  text-align: center;\n}\n\n.link-style li label {\n  width: 100px;\n  margin: 0 5px;\n}\n\n.link-style .col-md-2 > * {\n  line-height: 2em;\n}\n\n#wrap.o_editable .fa {\n  cursor: pointer;\n}\n\n.parallax .oe_structure > .oe_drop_zone:first-child {\n  top: 16px;\n}\n\n.parallax .oe_structure > .oe_drop_zone:last-child {\n  bottom: 16px;\n}\n\n.editor_enable .o_add_language {\n  display: none !important;\n}\n\n.editor_enable .o_facebook_page:not(.o_facebook_preview) iframe {\n  pointer-events: none;\n}\n\n.editor_enable .o_facebook_page:not(.o_facebook_preview) .o_facebook_alert .o_add_facebook_page {\n  cursor: pointer;\n}\n\nbody.editor_enable .s_countdown .s_countdown_enable_preview {\n  display: initial !important;\n}\n\nbody.editor_enable .s_countdown .s_countdown_none {\n  display: none !important;\n}\n\nbody.editor_enable .s_dynamic [data-url] {\n  cursor: inherit;\n}\n\n.editor_enable.o_animated_text_highlighted .o_animated_text {\n  position: relative;\n}\n\n.editor_enable.o_animated_text_highlighted .o_animated_text:after {\n  content: \"\";\n  pointer-events: none;\n  position: absolute;\n  top: 0;\n  left: 0;\n  bottom: 0;\n  right: 0;\n  z-index: 1;\n  border: 1px dotted white;\n  background-color: rgba(173, 255, 47, 0.2);\n}\n\n.editor_enable .s_website_form input:not(.o_translatable_attribute), .editor_enable .s_searchbar_input input:not(.o_translatable_attribute), .editor_enable .js_subscribe input:not(.o_translatable_attribute), .editor_enable .s_group input:not(.o_translatable_attribute), .editor_enable .s_donation_form input:not(.o_translatable_attribute) {\n  pointer-events: none;\n}\n\n.editor_enable .s_website_form [data-toggle=\"datetimepicker\"], .editor_enable .s_website_form textarea:not(.o_translatable_attribute):not(.o_translatable_text) {\n  pointer-events: none;\n}\n\n.o_homepage_editor_welcome_message {\n  padding-top: 128px;\n  padding-bottom: 128px;\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Ubuntu, \"Liberation Sans\", Arial, \"Odoo Unicode Support Noto\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n}\n\n", "\n/* /website_livechat/static/src/scss/website_livechat.edit_mode.scss */\n\nbody.editor_enable .o_livechat_button, body.editor_enable .o_thread_window {\n  display: none;\n}"], "file": "/web/assets/13871-d553735/1/website.assets_wysiwyg.css", "sourceRoot": "../../../../"}