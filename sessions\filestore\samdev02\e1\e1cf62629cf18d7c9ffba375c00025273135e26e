{"version": 12, "sheets": [{"id": "sheet1", "name": "Dashboard", "colNumber": 7, "rowNumber": 44, "rows": {"6": {"size": 40}, "18": {"size": 40}, "19": {"size": 40}, "31": {"size": 40}, "32": {"size": 40}}, "cols": {"0": {"size": 275}, "1": {"size": 100}, "2": {"size": 100}, "3": {"size": 50}, "4": {"size": 275}, "5": {"size": 100}, "6": {"size": 100}}, "merges": [], "cells": {"A7": {"style": 1, "content": "[Stock Quantity by Location](odoo://view/{\"viewType\":\"graph\",\"action\":{\"domain\":[[\"location_id.usage\",\"=\",\"internal\"]],\"context\":{\"group_by\":[\"location_id\"],\"graph_measure\":\"quantity\",\"graph_mode\":\"pie\",\"graph_groupbys\":[\"location_id\"]},\"modelName\":\"stock.quant\",\"views\":[[false,\"list\"],[false,\"form\"],[false,\"pivot\"],[false,\"graph\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Stock Quantity by Location\"})", "border": 1}, "A19": {"style": 1, "content": "[Top Locations](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[[\"location_id.usage\",\"=\",\"internal\"]],\"context\":{\"group_by\":[\"location_id\"],\"pivot_measures\":[\"reserved_quantity\",\"quantity\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"location_id\"]},\"modelName\":\"stock.quant\",\"views\":[[false,\"list\"],[false,\"form\"],[false,\"pivot\"],[false,\"graph\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Top Locations\"})", "border": 1}, "A20": {"style": 2, "content": "=_t(\"Location\")", "border": 2}, "A21": {"style": 3, "content": "=ODOO.PIVOT.HEADER(2,\"#location_id\",1)"}, "A22": {"style": 4, "content": "=ODOO.PIVOT.HEADER(2,\"#location_id\",2)"}, "A23": {"style": 3, "content": "=ODOO.PIVOT.HEADER(2,\"#location_id\",3)"}, "A24": {"style": 4, "content": "=ODOO.PIVOT.HEADER(2,\"#location_id\",4)"}, "A25": {"style": 3, "content": "=ODOO.PIVOT.HEADER(2,\"#location_id\",5)"}, "A26": {"style": 4, "content": "=ODOO.PIVOT.HEADER(2,\"#location_id\",6)"}, "A27": {"style": 3, "content": "=ODOO.PIVOT.HEADER(2,\"#location_id\",7)"}, "A28": {"style": 4, "content": "=ODOO.PIVOT.HEADER(2,\"#location_id\",8)"}, "A29": {"style": 3, "content": "=ODOO.PIVOT.HEADER(2,\"#location_id\",9)"}, "A30": {"style": 4, "content": "=ODOO.PIVOT.HEADER(2,\"#location_id\",10)"}, "A32": {"style": 1, "content": "[Top Lots / Serial Numbers](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[\"&\",[\"location_id.usage\",\"=\",\"internal\"],[\"lot_id\",\"!=\",false]],\"context\":{\"group_by\":[\"lot_id\"],\"pivot_measures\":[\"reserved_quantity\",\"quantity\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"lot_id\"]},\"modelName\":\"stock.quant\",\"views\":[[false,\"list\"],[false,\"form\"],[false,\"pivot\"],[false,\"graph\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Top Lots / Serial Numbers\"})", "border": 1}, "A33": {"style": 2, "content": "Lot/SN", "border": 2}, "A34": {"style": 3, "content": "=ODOO.PIVOT.HEADER(3,\"#lot_id\",1)"}, "A35": {"style": 4, "content": "=ODOO.PIVOT.HEADER(3,\"#lot_id\",2)"}, "A36": {"style": 3, "content": "=ODOO.PIVOT.HEADER(3,\"#lot_id\",3)"}, "A37": {"style": 4, "content": "=ODOO.PIVOT.HEADER(3,\"#lot_id\",4)"}, "A38": {"style": 3, "content": "=ODOO.PIVOT.HEADER(3,\"#lot_id\",5)"}, "A39": {"style": 4, "content": "=ODOO.PIVOT.HEADER(3,\"#lot_id\",6)"}, "A40": {"style": 3, "content": "=ODOO.PIVOT.HEADER(3,\"#lot_id\",7)"}, "A41": {"style": 4, "content": "=ODOO.PIVOT.HEADER(3,\"#lot_id\",8)"}, "A42": {"style": 3, "content": "=ODOO.PIVOT.HEADER(3,\"#lot_id\",9)"}, "A43": {"style": 4}, "B7": {"style": 5, "border": 1}, "B19": {"style": 5, "border": 1}, "B20": {"style": 6, "content": "=_t(\"Reserved\")", "border": 2}, "B21": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(2,\"reserved_quantity\",\"#location_id\",1)"}, "B22": {"format": 1, "content": "=ODOO.PIVOT(2,\"reserved_quantity\",\"#location_id\",2)"}, "B23": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(2,\"reserved_quantity\",\"#location_id\",3)"}, "B24": {"format": 1, "content": "=ODOO.PIVOT(2,\"reserved_quantity\",\"#location_id\",4)"}, "B25": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(2,\"reserved_quantity\",\"#location_id\",5)"}, "B26": {"format": 1, "content": "=ODOO.PIVOT(2,\"reserved_quantity\",\"#location_id\",6)"}, "B27": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(2,\"reserved_quantity\",\"#location_id\",7)"}, "B28": {"format": 1, "content": "=ODOO.PIVOT(2,\"reserved_quantity\",\"#location_id\",8)"}, "B29": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(2,\"reserved_quantity\",\"#location_id\",9)"}, "B30": {"format": 1, "content": "=ODOO.PIVOT(2,\"reserved_quantity\",\"#location_id\",10)"}, "B32": {"style": 5, "border": 1}, "B33": {"style": 6, "content": "=_t(\"Reserved\")", "border": 2}, "B34": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(3,\"reserved_quantity\",\"#lot_id\",1)"}, "B35": {"format": 1, "content": "=ODOO.PIVOT(3,\"reserved_quantity\",\"#lot_id\",2)"}, "B36": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(3,\"reserved_quantity\",\"#lot_id\",3)"}, "B37": {"format": 1, "content": "=ODOO.PIVOT(3,\"reserved_quantity\",\"#lot_id\",4)"}, "B38": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(3,\"reserved_quantity\",\"#lot_id\",5)"}, "B39": {"format": 1, "content": "=ODOO.PIVOT(3,\"reserved_quantity\",\"#lot_id\",6)"}, "B40": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(3,\"reserved_quantity\",\"#lot_id\",7)"}, "B41": {"format": 1, "content": "=ODOO.PIVOT(3,\"reserved_quantity\",\"#lot_id\",8)"}, "B42": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(3,\"reserved_quantity\",\"#lot_id\",9)"}, "B43": {"format": 1}, "C7": {"style": 5, "border": 1}, "C19": {"style": 5, "border": 1}, "C20": {"style": 6, "content": "=_t(\"On Hand\")", "border": 2}, "C21": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(2,\"quantity\",\"#location_id\",1)"}, "C22": {"format": 1, "content": "=ODOO.PIVOT(2,\"quantity\",\"#location_id\",2)"}, "C23": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(2,\"quantity\",\"#location_id\",3)"}, "C24": {"format": 1, "content": "=ODOO.PIVOT(2,\"quantity\",\"#location_id\",4)"}, "C25": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(2,\"quantity\",\"#location_id\",5)"}, "C26": {"format": 1, "content": "=ODOO.PIVOT(2,\"quantity\",\"#location_id\",6)"}, "C27": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(2,\"quantity\",\"#location_id\",7)"}, "C28": {"format": 1, "content": "=ODOO.PIVOT(2,\"quantity\",\"#location_id\",8)"}, "C29": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(2,\"quantity\",\"#location_id\",9)"}, "C30": {"format": 1, "content": "=ODOO.PIVOT(2,\"quantity\",\"#location_id\",10)"}, "C32": {"style": 5, "border": 1}, "C33": {"style": 6, "content": "=_t(\"On Hand\")", "border": 2}, "C34": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(3,\"quantity\",\"#lot_id\",1)"}, "C35": {"format": 1, "content": "=ODOO.PIVOT(3,\"quantity\",\"#lot_id\",2)"}, "C36": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(3,\"quantity\",\"#lot_id\",3)"}, "C37": {"format": 1, "content": "=ODOO.PIVOT(3,\"quantity\",\"#lot_id\",4)"}, "C38": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(3,\"quantity\",\"#lot_id\",5)"}, "C39": {"format": 1, "content": "=ODOO.PIVOT(3,\"quantity\",\"#lot_id\",6)"}, "C40": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(3,\"quantity\",\"#lot_id\",7)"}, "C41": {"format": 1, "content": "=ODOO.PIVOT(3,\"quantity\",\"#lot_id\",8)"}, "C42": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(3,\"quantity\",\"#lot_id\",9)"}, "C43": {"format": 1}, "D7": {"style": 5, "border": 1}, "E7": {"style": 5, "border": 1}, "E19": {"style": 1, "content": "[Top Products](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[[\"location_id.usage\",\"=\",\"internal\"]],\"context\":{\"group_by\":[\"product_id\"],\"pivot_measures\":[\"reserved_quantity\",\"quantity\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"product_id\"]},\"modelName\":\"stock.quant\",\"views\":[[false,\"list\"],[false,\"form\"],[false,\"pivot\"],[false,\"graph\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Top Products\"})", "border": 1}, "E20": {"style": 2, "content": "=_t(\"Product\")", "border": 2}, "E21": {"style": 3, "content": "=ODOO.PIVOT.HEADER(4,\"#product_id\",1)"}, "E22": {"style": 4, "content": "=ODOO.PIVOT.HEADER(4,\"#product_id\",2)"}, "E23": {"style": 3, "content": "=ODOO.PIVOT.HEADER(4,\"#product_id\",3)"}, "E24": {"style": 4, "content": "=ODOO.PIVOT.HEADER(4,\"#product_id\",4)"}, "E25": {"style": 3, "content": "=ODOO.PIVOT.HEADER(4,\"#product_id\",5)"}, "E26": {"style": 4, "content": "=ODOO.PIVOT.HEADER(4,\"#product_id\",6)"}, "E27": {"style": 3, "content": "=ODOO.PIVOT.HEADER(4,\"#product_id\",7)"}, "E28": {"style": 4, "content": "=ODOO.PIVOT.HEADER(4,\"#product_id\",8)"}, "E29": {"style": 3, "content": "=ODOO.PIVOT.HEADER(4,\"#product_id\",9)"}, "E30": {"style": 4, "content": "=ODOO.PIVOT.HEADER(4,\"#product_id\",10)"}, "F7": {"style": 5, "border": 1}, "F19": {"style": 5, "border": 1}, "F20": {"style": 6, "content": "=_t(\"Reserved\")", "border": 2}, "F21": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(4,\"reserved_quantity\",\"#product_id\",1)"}, "F22": {"format": 1, "content": "=ODOO.PIVOT(4,\"reserved_quantity\",\"#product_id\",2)"}, "F23": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(4,\"reserved_quantity\",\"#product_id\",3)"}, "F24": {"format": 1, "content": "=ODOO.PIVOT(4,\"reserved_quantity\",\"#product_id\",4)"}, "F25": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(4,\"reserved_quantity\",\"#product_id\",5)"}, "F26": {"format": 1, "content": "=ODOO.PIVOT(4,\"reserved_quantity\",\"#product_id\",6)"}, "F27": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(4,\"reserved_quantity\",\"#product_id\",7)"}, "F28": {"format": 1, "content": "=ODOO.PIVOT(4,\"reserved_quantity\",\"#product_id\",8)"}, "F29": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(4,\"reserved_quantity\",\"#product_id\",9)"}, "F30": {"format": 1, "content": "=ODOO.PIVOT(4,\"reserved_quantity\",\"#product_id\",10)"}, "G7": {"style": 5, "border": 1}, "G19": {"style": 5, "border": 1}, "G20": {"style": 6, "content": "=_t(\"On Hand\")", "border": 2}, "G21": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(4,\"quantity\",\"#product_id\",1)"}, "G22": {"format": 1, "content": "=ODOO.PIVOT(4,\"quantity\",\"#product_id\",2)"}, "G23": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(4,\"quantity\",\"#product_id\",3)"}, "G24": {"format": 1, "content": "=ODOO.PIVOT(4,\"quantity\",\"#product_id\",4)"}, "G25": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(4,\"quantity\",\"#product_id\",5)"}, "G26": {"format": 1, "content": "=ODOO.PIVOT(4,\"quantity\",\"#product_id\",6)"}, "G27": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(4,\"quantity\",\"#product_id\",7)"}, "G28": {"format": 1, "content": "=ODOO.PIVOT(4,\"quantity\",\"#product_id\",8)"}, "G29": {"style": 7, "format": 1, "content": "=ODOO.PIVOT(4,\"quantity\",\"#product_id\",9)"}, "G30": {"format": 1, "content": "=ODOO.PIVOT(4,\"quantity\",\"#product_id\",10)"}, "A8": {"border": 2}, "B8": {"border": 2}, "C8": {"border": 2}, "D8": {"border": 2}, "E8": {"border": 2}, "F8": {"border": 2}, "G8": {"border": 2}}, "conditionalFormats": [], "figures": [{"id": "09d4da42-d66e-47b4-bc71-c6cfab7be7b9", "x": 0, "y": 0, "width": 198, "height": 120, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "difference", "title": "Inventory Value", "type": "scorecard", "background": "", "keyValue": "Data!B2"}}, {"id": "d84bdd18-3c66-4153-ae2e-7f4ec1672cfb", "x": 0, "y": 184, "width": 657, "height": 238, "tag": "chart", "data": {"title": "", "background": "#FFFFFF", "legendPosition": "right", "metaData": {"groupBy": ["location_id"], "measure": "quantity", "order": "DESC", "resModel": "stock.quant"}, "searchParams": {"comparison": null, "context": {"mail_notify_force_send": false, "always_show_loc": 1, "inventory_mode": true, "inventory_report_mode": true}, "domain": [["location_id.usage", "=", "internal"]], "groupBy": ["location_id"], "orderBy": []}, "type": "odoo_pie"}}], "areGridLinesVisible": true, "isVisible": true}, {"id": "fb6d5d91-04cf-4c22-953a-a00c4e8f19e4", "name": "Data", "colNumber": 23, "rowNumber": 100, "rows": {}, "cols": {"0": {"size": 126.73291015625}}, "merges": [], "cells": {"A1": {"style": 2, "content": "=_t(\"KPI\")"}, "A2": {"content": "=_t(\"Total inventory value\")"}, "B1": {"style": 2, "content": "=_t(\"Current\")"}, "B2": {"style": 7, "content": "=FORMAT.LARGE.NUMBER(ODOO.PIVOT(1,\"value\"))"}}, "conditionalFormats": [], "figures": [], "areGridLinesVisible": true, "isVisible": true}], "entities": {}, "styles": {"1": {"textColor": "#01666b", "fontSize": 16, "bold": true}, "2": {"bold": true}, "3": {"fillColor": "#f2f2f2", "textColor": "#741b47"}, "4": {"textColor": "#741b47"}, "5": {"fontSize": 16, "bold": true}, "6": {"bold": true, "align": "right"}, "7": {"fillColor": "#f2f2f2"}}, "formats": {"1": "#,##0"}, "borders": {"1": {"bottom": ["thin", "#000"]}, "2": {"top": ["thin", "#000"]}}, "revisionId": "b36049b5-b8cf-4375-9c14-6c99017ea26c", "chartOdooMenusReferences": {"d84bdd18-3c66-4153-ae2e-7f4ec1672cfb": "stock.menu_stock_root", "09d4da42-d66e-47b4-bc71-c6cfab7be7b9": "stock_account.menu_valuation"}, "odooVersion": 4, "lists": {}, "listNextId": 1, "pivots": {"1": {"colGroupBys": [], "context": {}, "domain": [], "id": "1", "measures": [{"field": "value"}], "model": "stock.valuation.layer", "rowGroupBys": [], "name": "total inventory value", "sortedColumn": null}, "2": {"colGroupBys": [], "context": {"mail_notify_force_send": false, "always_show_loc": 1, "inventory_mode": true, "inventory_report_mode": true}, "domain": [["location_id.usage", "=", "internal"]], "id": "2", "measures": [{"field": "reserved_quantity"}, {"field": "quantity"}], "model": "stock.quant", "rowGroupBys": ["location_id"], "name": "Inventory by Location", "sortedColumn": {"groupId": [[], []], "measure": "quantity", "order": "desc"}}, "3": {"colGroupBys": [], "context": {"mail_notify_force_send": false, "always_show_loc": 1, "inventory_mode": true, "inventory_report_mode": true}, "domain": ["&", ["location_id.usage", "=", "internal"], ["lot_id", "!=", false]], "id": "3", "measures": [{"field": "reserved_quantity"}, {"field": "quantity"}], "model": "stock.quant", "rowGroupBys": ["lot_id"], "name": "Inventory by Lot/Serial Number", "sortedColumn": {"groupId": [[], []], "measure": "quantity", "order": "desc"}}, "4": {"colGroupBys": [], "context": {"mail_notify_force_send": false, "always_show_loc": 1, "inventory_mode": true, "inventory_report_mode": true}, "domain": [["location_id.usage", "=", "internal"]], "id": "4", "measures": [{"field": "reserved_quantity"}, {"field": "quantity"}], "model": "stock.quant", "rowGroupBys": ["product_id"], "name": "Inventory by Product", "sortedColumn": {"groupId": [[], []], "measure": "quantity", "order": "desc"}}}, "pivotNextId": 5, "globalFilters": [{"id": "62f6d1bd-9c76-48d7-b95a-3f5d6cb505df", "type": "relation", "label": "Location", "modelName": "stock.location", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"1": {"field": "stock_move_id.location_dest_id", "type": "many2one"}, "2": {"field": "location_id", "type": "many2one"}, "3": {"field": "location_id", "type": "many2one"}, "4": {"field": "location_id", "type": "many2one"}}, "listFields": {}, "graphFields": {"d84bdd18-3c66-4153-ae2e-7f4ec1672cfb": {"field": "location_id", "type": "many2one"}}}, {"id": "8dea5478-ff09-41fc-ada0-cf23c66e4f8b", "type": "relation", "label": "Product", "modelName": "product.product", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"1": {"field": "product_id", "type": "many2one"}, "2": {"field": "product_id", "type": "many2one"}, "3": {"field": "product_id", "type": "many2one"}, "4": {"field": "product_id", "type": "many2one"}}, "listFields": {}, "graphFields": {"d84bdd18-3c66-4153-ae2e-7f4ec1672cfb": {"field": "product_id", "type": "many2one"}}}, {"id": "0725cf6d-c980-46a1-bd7b-bd75e34348e7", "type": "relation", "label": "Lot/Serial", "modelName": "stock.lot", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"1": {"field": "stock_move_id.lot_ids", "type": "many2many"}, "2": {"field": "lot_id", "type": "many2one"}, "3": {"field": "lot_id", "type": "many2one"}, "4": {"field": "lot_id", "type": "many2one"}}, "listFields": {}, "graphFields": {"d84bdd18-3c66-4153-ae2e-7f4ec1672cfb": {"field": "lot_id", "type": "many2one"}}}]}