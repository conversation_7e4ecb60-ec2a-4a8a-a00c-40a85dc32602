from typing import Annotated
from odoo.exceptions import UserError
from ..schemas import ApmTraitDatas, RecordDatas,APMLeadDatas
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks, Path
from ..dependencies import authenticated_fastapi_endpoint
from odoo.addons.fastapi.models.fastapi_endpoint import FastapiEndpoint as ThFastapi
import time

router = APIRouter(tags=["apm"])


def write_log(self, data: object, state: str, duration: str, function_name: str = None, description: str = None):
    self.env['th.log.api'].create({
        'state': state,
        'th_model': str(self._name),
        'th_description': description,
        'th_input_data': str(data),
        'th_function_call': function_name,
        'is_log_fast_api': True,
        'th_fastapi_endpoint_id': self.id,
        'th_time_response': duration,
    })


@router.post("/api/apmleads/{id_b2b}")
def create_apm_lead(
    ApmLeadDatas: list[RecordDatas],
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint), ], background_tasks: BackgroundTasks,
    id_b2b: int = Path(..., ge=1),
):
    values_b2b = {}
    start = time.perf_counter()
    try:
        if fastapi:
            results = []
            for record in ApmLeadDatas:
                values_b2b = record.th_data_apm.model_dump(exclude_none=True, exclude_unset=True)
                partner_b2b_id = values_b2b.get('th_partner_id', None)
                partner_b2b_info = values_b2b.get('partner_info').copy() if values_b2b.get('partner_info') else None
                # val_samp
                values = fastapi.env['th.apm'].map_data_apm_from_sync(values_b2b)
                partner_info = values.pop('partner_info') if values_b2b.get('partner_info') else None
                mapid = fastapi.env['th.mapping.id'].search([
                    ('th_external_id', '=', id_b2b),
                    ('th_model_name', '=', 'th.apm'),
                    ('th_system', '=', 'b2b')
                ], limit=1)
                partner_sam = fastapi.env['th.mapping.id'].search([
                    ('th_external_id', '=', partner_b2b_id),
                    ('th_model_name', '=', 'res.partner'),
                    ('th_system', '=', 'b2b')
                ], limit=1)
                if partner_sam:
                    values['th_partner_id'] = partner_sam.th_internal_id
                if mapid and mapid.th_internal_id: # write
                    results = []
                    response = fastapi.env['th.apm'].browse(mapid.th_internal_id).sudo().with_context(th_sync=True).th_write_th_apm(values)
                    results.append({
                        "status": "success",
                        "response": response,
                        "id": record.id_b2b,
                    })
                    background_tasks.add_task(write_log, fastapi, values_b2b, 'success', str(round(time.perf_counter() - start, 4)), 'api/apmleads')
                    return results
                else: # create
                    partner_map = None
                    # chưa có partner
                    if not values['th_partner_id'] and partner_info:
                        if partner_info['country_id']:
                            country = fastapi.env['res.country'].search([('code', '=', partner_info['country_id'])],    limit=1)
                            partner_info['country_id'] = country.id
                        if partner_info['th_country_id']:
                            th_country = fastapi.env['res.country'].search([('code', '=', partner_info['th_country_id'])], limit=1)
                            partner_info['th_country_id'] = th_country.id
                        values_partner = fastapi.env['res.partner'].map_data_res_partner_from_sync(partner_info)
                        partner = fastapi.env['res.partner'].create(values_partner)
                        values['th_partner_id'] = partner.id
                        partner_map = partner
                    apm_lead = fastapi.env['th.apm'].sudo().with_context(th_sync=True).th_create_th_apm(values)
                    if apm_lead:
                        fastapi.env['th.mapping.id'].create({
                            'name': apm_lead.name,
                            'th_model_name': 'th.apm',
                            'th_internal_id': apm_lead.id,
                            'th_external_id': id_b2b,
                            'th_system': 'b2b',
                            'th_module_id': fastapi.env.ref('th_setup_parameters.th_apm_module').id,
                        })
                        if partner_map:
                            fastapi.env['th.mapping.id'].create({
                                'name': partner_map.name,
                                'th_model_name': 'res.partner',
                                'th_internal_id': partner_map.id,
                                'th_external_id': partner_b2b_id,
                                'th_system': 'b2b',
                            })

                    results.append({
                        'apm_lead': {'name': apm_lead.name},
                        'partner_id': {}
                    })
                    if not partner_b2b_info.get('th_customer_code', None):
                        for result in results:
                            result['partner_id']['th_customer_code'] = apm_lead.th_customer_code
                    background_tasks.add_task(write_log, fastapi, values_b2b, 'success', str(round(time.perf_counter() - start, 4)), 'api/apmleads')
                    print(round(time.perf_counter() - start, 4))
                    
                    # Tính thời gian xử lý tại SamP
                    th_processing_time = time.perf_counter() - start
                    results.append({
                        'processing_time': round(th_processing_time * 1000, 2)
                    })
                    return results

    except Exception as e:
        th_processing_time = time.perf_counter() - start
        write_log(fastapi, values_b2b, 'error', str(round(time.perf_counter() - start, 4)), 'api/apmleads', str(e))
        fastapi.env.cr.commit()
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/apmleads/{id_b2b}")
def delete_apm_lead(
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id_b2b: int = Path(..., ge=1)
):
    try:
        if fastapi:
            mapid = fastapi.env['th.mapping.id'].search([
                ('th_external_id', '=', id_b2b),
                ('th_model_name', '=', 'th.apm'),
                ('th_system', '=', 'b2b')
            ], limit=1)

            if not mapid or not mapid.th_internal_id:
                raise HTTPException(
                    status_code=404,
                    detail="Không tìm thấy bản ghi tương ứng trong SamP."
                )

            apm_lead = fastapi.env['th.apm'].browse(mapid.th_internal_id)
            if not apm_lead.exists():
                raise HTTPException(
                    status_code=404,
                    detail="Không tìm thấy cơ hội APM trong SamP."
                )

            apm_lead.sudo().with_context(th_sync=True).unlink()
            mapid.sudo().unlink()

            return {
                "message": "Đã xóa cơ hội APM thành công.",
                "id_b2b": id_b2b
            }

    except UserError as e:
        raise HTTPException(
            status_code=400,
            detail=f"Lỗi người dùng: {str(e)}"
        )

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Lỗi hệ thống: {str(e)}"
        )


@router.post("/api/thapmtrait/{id_b2b}")
def create_apm_trait(
    ApmTraitDatas: ApmTraitDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    id_b2b: int = Path(..., ge=0)
):
    try:
        if fastapi:
            values_b2b = ApmTraitDatas.model_dump(exclude_none=True, exclude_unset=True)
            values = fastapi.env['th.apm.trait'].map_data_apm_trait_from_sync(values_b2b)  # val_samp
            mapid = fastapi.env['th.mapping.id'].search([
                ('th_external_id', '=', id_b2b),
                ('th_model_name', '=', 'th.apm.trait'),
                ('th_system', '=', 'b2b')
            ], limit=1)

            if mapid and mapid.th_internal_id:  # write
                fastapi.env['th.ap.trait'].browse(mapid.th_internal_id).sudo().with_context(th_sync=True).write(values)
            else:  # create
                apm_trait = fastapi.env['th.apm.trait'].sudo().with_context(th_sync=True).create(values)
                if apm_trait:
                    fastapi.env['th.mapping.id'].create({
                        'name': apm_trait.name,
                        'th_model_name': 'th.apm.trait',
                        'th_internal_id': apm_trait.id,
                        'th_external_id': id_b2b,
                        'th_system': 'b2b',
                        'th_module_id': fastapi.env.ref('th_setup_parameters.th_apm_module').id,
                    })

                result = {
                    'apm_lead': {
                        'name': apm_trait.name,
                    },
                }
                return result

    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/api/formio/apmleads")
def create_lead_formio(
    data: APMLeadDatas,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
    background_tasks: BackgroundTasks
):
    start = time.perf_counter()
    try:
        if fastapi:
            # Khởi tạo các biến
            vals = data
            th_origin = False
            th_source_group_id = False
            th_partner_referred_id = False
            th_channel_id = fastapi.env.ref('th_setup_parameters.th_channel_unknown').id
            th_ownership = fastapi.env['th.ownership.unit'].search(
                [('th_code', '=', vals.get('context').get('th_ownership_code'))], limit=1
            )
            check_module = fastapi.env['ir.config_parameter'].sudo().get_param('th_check_partner_module')
            
            # Xử lý th_origin từ th_warehouse_code
            if vals.get('th_warehouse_code', False):
                th_origin = fastapi.env['th.origin'].search(
                    [('th_code', '=', vals.get('th_warehouse_code'))], limit=1
                )
            
            # Xử lý th_source_group_id từ th_utm_source
            if vals.get('th_utm_source', False):
                th_source_group = fastapi.env['th.source.group'].search(
                    [('name', '=', vals.get('th_utm_source', False))], limit=1
                )
                if th_source_group:
                    th_source_group_id = th_source_group.id
                else:
                    th_source_group_id = fastapi.env['th.source.group'].create({
                        'name': vals.get('th_utm_source', False),
                    }).id
            # Xử lý th_partner_referred_id từ th_affiliate_code
            if vals.get('th_affiliate_code', False):
                th_partner_referred_id = fastapi.env['res.partner'].search(
                    [('th_affiliate_code', '=', vals.get('th_affiliate_code'))], limit=1
                )
            domain = []
            if vals.get('th_phone', False):
                domain = ['|', ('phone', '=', vals.get('th_phone')), ('th_phone2', '=', vals.get('th_phone'))]
            if vals.get('th_email', False):
                if domain:
                    domain = ['|'] + domain + [('email', '=', vals.get('th_email'))]
                else:
                    domain = [('email', '=', vals.get('th_email'))]
            if check_module:
                domain.append(('th_module_ids', 'in', fastapi.env.ref('th_setup_parameters.th_apm_module').ids))
            
            partner_aff = False
            if domain:
                partner_aff = fastapi.env['res.partner'].search(domain, limit=1)
                
                if partner_aff:
                    if partner_aff.phone and not partner_aff.th_phone2 and vals.get('th_phone', False) and partner_aff.phone != vals.get('th_phone', False):
                        partner_aff.write({'th_phone2': vals.get('th_phone')})
                    elif not partner_aff.phone and vals.get('th_phone', False):
                        partner_aff.write({'phone': vals.get('th_phone')})
                    elif (partner_aff.th_phone2 and partner_aff.phone and vals.get('th_phone', False) 
                          and vals.get('th_phone', False) not in [partner_aff.th_phone2, partner_aff.phone]):
                        from markupsafe import Markup
                        partner_aff.write({
                            'comment': Markup(
                                (partner_aff.comment or '') + 'Số điện thoại mới: ' + vals.get('th_phone', False)
                            )
                        })

            campaign = fastapi.env['th.apm.campaign'].search(
                [('id', '=', fastapi.env.ref('th_apm.campaign_lead_formio').id)], limit=1
            )
            if not partner_aff:
                partner_aff = fastapi.env['res.partner'].create({
                    'name': vals.get('th_customer', vals.get('th_phone', 'Khách hàng mới')),
                    'phone': vals.get('th_phone', False),
                    'email': vals.get('th_email', False),
                    'th_module_ids': [(4, fastapi.env.ref('th_setup_parameters.th_apm_module').id)],
                })

            # Chuẩn bị dữ liệu cho opportunity mới
            new_opportunity = {
                'th_opportunity_aff_id': vals.get('id', False),
                'th_partner_id': partner_aff.id,
                'th_campaign_id': campaign.id if campaign else False,
                'th_apm_email': vals.get('th_email', False),
                'th_apm_phone': vals.get('th_phone', False),
                'th_origin_id': th_origin.id if th_origin else False,
                'th_partner_reference_id': th_partner_referred_id.id if th_partner_referred_id else False,
                'th_ownership_unit_id': th_ownership.id if th_ownership else False,
                'th_description': vals.get('th_description', False),
                'th_status_category_id': fastapi.env.ref('th_apm.th_no_process_category').id,
                'th_utm_source': vals.get('th_utm_source', False),
                'th_utm_medium': vals.get('th_utm_medium', False),
                'th_utm_campaign': vals.get('th_utm_campaign', False),
                'th_utm_term': vals.get('th_utm_term', False),
                'th_utm_content': vals.get('th_utm_content', False),
                'th_source_group_id': th_source_group_id,
                'th_channel_id': th_channel_id,
            }
            
            if vals.get("context").get('th_form_id') and vals.get("context").get('aff_apm_lead'):
                th_formio_pro_id = fastapi.env['th.formio.builder.field.aff.default'].search(
                    [('th_uuid', '=', vals.get("context").get('th_form_id'))], limit=1
                )
                if th_formio_pro_id:
                    new_opportunity.update({
                        'th_apm_team_id': th_formio_pro_id.action_assign_leads_apm(),
                        'th_apm_dividing_ring_id': th_formio_pro_id.th_apm_dividing_ring_id.id if th_formio_pro_id.th_apm_dividing_ring_id else False,
                    })
                    # Gán người chăm sóc theo vòng chia
                    if th_formio_pro_id.th_apm_dividing_ring_id:
                        user_id = th_formio_pro_id.th_apm_dividing_ring_id.action_assign_leads_dividing_ring()
                        if user_id:
                            new_opportunity.update({'th_user_id': user_id})

            # Tạo APM lead
            apm_lead = fastapi.env['th.apm'].sudo().create(new_opportunity)
            
            result = {}
            if vals.get('id', False) and apm_lead:
                result = {
                    'apm_id': apm_lead.id, 
                    'duplicate': False,
                }
            result['th_processing_time'] = round(time.perf_counter() - start, 4) * 1000
            
            return result
            
    except Exception as e:
        fastapi.env.cr.commit()
        raise HTTPException(status_code=400, detail=str(e))
