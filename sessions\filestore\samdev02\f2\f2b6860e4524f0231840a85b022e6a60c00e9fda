

/* /web/static/lib/bootstrap/scss/_functions.scss */



/* /web/static/lib/bootstrap/scss/_mixins.scss */



/* /web/static/src/scss/mixins_forwardport.scss */



/* /web/static/src/scss/bs_mixins_overrides.scss */



/* /web/static/src/legacy/scss/utils.scss */



/* /web_enterprise/static/src/scss/primary_variables.scss */



/* /web/static/src/scss/primary_variables.scss */



/* /web_enterprise/static/src/core/notifications/notifications.variables.scss */



/* /web_enterprise/static/src/webclient/home_menu/home_menu.variables.scss */



/* /web_enterprise/static/src/webclient/navbar/navbar.variables.scss */



/* /web/static/src/core/notifications/notification.variables.scss */



/* /web/static/src/search/control_panel/control_panel.variables.scss */



/* /web/static/src/search/search_panel/search_panel.variables.scss */



/* /web/static/src/views/form/form.variables.scss */



/* /web/static/src/views/kanban/kanban.variables.scss */



/* /web/static/src/webclient/burger_menu/burger_menu.variables.scss */



/* /web/static/src/webclient/navbar/navbar.variables.scss */



/* /base/static/src/scss/onboarding.variables.scss */



/* /mail/static/src/scss/variables/primary_variables.scss */



/* /web_editor/static/src/scss/web_editor.variables.scss */



/* /web_editor/static/src/scss/wysiwyg.variables.scss */



/* /portal/static/src/scss/primary_variables.scss */



/* /account/static/src/scss/variables.scss */

@keyframes animate-red {
  0% {
    color: red;
  }
  100% {
    color: inherit;
  }
}

.animate {
  animation: animate-red 1s ease;
}



/* /website/static/src/scss/primary_variables.scss */



/* /website/static/src/scss/options/user_values.scss */



/* /website/static/src/scss/options/colors/user_color_palette.scss */



/* /website/static/src/scss/options/colors/user_gray_color_palette.scss */



/* /website/static/src/scss/options/colors/user_theme_color_palette.scss */



/* /web_gantt/static/src/scss/web_gantt.variables.scss */



/* /documents/static/src/scss/documents.variables.scss */



/* /hr_org_chart/static/src/scss/variables.scss */



/* /website/static/src/snippets/s_badge/000_variables.scss */



/* /website/static/src/snippets/s_product_list/000_variables.scss */



/* /website/static/src/scss/secondary_variables.scss */



/* /web_enterprise/static/src/scss/secondary_variables.scss */



/* /web/static/src/scss/secondary_variables.scss */



/* /web_editor/static/src/scss/secondary_variables.scss */



/* /website/static/src/scss/user_custom_bootstrap_overridden.scss */



/* /website/static/src/scss/bootstrap_overridden.scss */



/* /portal/static/src/scss/bootstrap_overridden.scss */



/* /web_editor/static/src/scss/bootstrap_overridden.scss */



/* /web/static/src/scss/bootstrap_overridden_frontend.scss */



/* /web/static/src/scss/pre_variables.scss */



/* /web/static/lib/bootstrap/scss/_variables.scss */



/* /survey/static/src/scss/survey_templates_form.scss */

/**********************************************************
  Remove website backend redirection button : Should be
  done in website survey but we won't do a bridge module
  only for this.
  TODO: SmartPeople Fixme - cleaner solution? be my guest!
 **********************************************************/
nav.o_frontend_to_backend_nav {
  display: none !important;
}

/**********************************************************
                        Common Style
 **********************************************************/
.o_survey_background {
  height: 100%;
  overflow: auto;
  transition: box-shadow 0.3s ease-in-out;
  box-shadow: inset 0 0 0 10000px rgba(255, 255, 255, 0.7);
  background: no-repeat fixed center;
  background-size: cover;
}

.o_survey_background.o_survey_background_transition {
  box-shadow: inset 0 0 0 10000px white;
}

.o_survey_wrap {
  min-height: 100%;
}

_::-webkit-full-page-media, _:future, :root .o_survey_wrap {
  min-height: 90vh;
}

.o_survey_progress_wrapper {
  min-width: 7rem;
  max-width: 11rem;
}

.o_survey_progress_wrapper .o_survey_progress {
  height: 0.5em;
}

.o_survey_navigation_wrapper .o_survey_navigation_submit {
  cursor: pointer;
}

.o_survey_navigation_wrapper .o_survey_navigation_submit:disabled {
  cursor: default;
  opacity: 1;
}

.o_survey_navigation_wrapper .o_survey_navigation_submit:disabled i {
  opacity: .3;
}

.o_survey_timer {
  min-height: 1.2rem;
}

.o_survey_brand_message {
  background-color: rgba(255, 255, 255, 0.7);
}

.o_survey_form .o_survey_question_error, .o_survey_print .o_survey_question_error, .o_survey_session_manage .o_survey_question_error, .o_survey_quick_access .o_survey_question_error {
  height: 0px;
  transition: height .5s ease;
  line-height: 4rem;
}

.o_survey_form .o_survey_question_error.slide_in, .o_survey_print .o_survey_question_error.slide_in, .o_survey_session_manage .o_survey_question_error.slide_in, .o_survey_quick_access .o_survey_question_error.slide_in {
  height: 4rem;
}

.o_survey_form fieldset[disabled] .o_survey_question_text_box,
.o_survey_form fieldset[disabled] .o_survey_question_date,
.o_survey_form fieldset[disabled] .o_survey_question_datetime,
.o_survey_form fieldset[disabled] .o_survey_question_numerical_box, .o_survey_print fieldset[disabled] .o_survey_question_text_box,
.o_survey_print fieldset[disabled] .o_survey_question_date,
.o_survey_print fieldset[disabled] .o_survey_question_datetime,
.o_survey_print fieldset[disabled] .o_survey_question_numerical_box, .o_survey_session_manage fieldset[disabled] .o_survey_question_text_box,
.o_survey_session_manage fieldset[disabled] .o_survey_question_date,
.o_survey_session_manage fieldset[disabled] .o_survey_question_datetime,
.o_survey_session_manage fieldset[disabled] .o_survey_question_numerical_box, .o_survey_quick_access fieldset[disabled] .o_survey_question_text_box,
.o_survey_quick_access fieldset[disabled] .o_survey_question_date,
.o_survey_quick_access fieldset[disabled] .o_survey_question_datetime,
.o_survey_quick_access fieldset[disabled] .o_survey_question_numerical_box {
  padding-left: 0px;
}

.o_survey_form .o_survey_question_text_box,
.o_survey_form .o_survey_question_date,
.o_survey_form .o_survey_question_datetime,
.o_survey_form .o_survey_question_numerical_box, .o_survey_print .o_survey_question_text_box,
.o_survey_print .o_survey_question_date,
.o_survey_print .o_survey_question_datetime,
.o_survey_print .o_survey_question_numerical_box, .o_survey_session_manage .o_survey_question_text_box,
.o_survey_session_manage .o_survey_question_date,
.o_survey_session_manage .o_survey_question_datetime,
.o_survey_session_manage .o_survey_question_numerical_box, .o_survey_quick_access .o_survey_question_text_box,
.o_survey_quick_access .o_survey_question_date,
.o_survey_quick_access .o_survey_question_datetime,
.o_survey_quick_access .o_survey_question_numerical_box {
  border: 0px;
  border-bottom: 1px solid #35979c;
}

.o_survey_form .o_survey_question_text_box:disabled,
.o_survey_form .o_survey_question_date:disabled,
.o_survey_form .o_survey_question_datetime:disabled,
.o_survey_form .o_survey_question_numerical_box:disabled, .o_survey_print .o_survey_question_text_box:disabled,
.o_survey_print .o_survey_question_date:disabled,
.o_survey_print .o_survey_question_datetime:disabled,
.o_survey_print .o_survey_question_numerical_box:disabled, .o_survey_session_manage .o_survey_question_text_box:disabled,
.o_survey_session_manage .o_survey_question_date:disabled,
.o_survey_session_manage .o_survey_question_datetime:disabled,
.o_survey_session_manage .o_survey_question_numerical_box:disabled, .o_survey_quick_access .o_survey_question_text_box:disabled,
.o_survey_quick_access .o_survey_question_date:disabled,
.o_survey_quick_access .o_survey_question_datetime:disabled,
.o_survey_quick_access .o_survey_question_numerical_box:disabled {
  color: black !important;
  border-color: #6C757D;
  border-bottom: 1px solid #6C757D;
}

.o_survey_form .o_survey_question_text_box:focus,
.o_survey_form .o_survey_question_date:focus,
.o_survey_form .o_survey_question_datetime:focus,
.o_survey_form .o_survey_question_numerical_box:focus, .o_survey_print .o_survey_question_text_box:focus,
.o_survey_print .o_survey_question_date:focus,
.o_survey_print .o_survey_question_datetime:focus,
.o_survey_print .o_survey_question_numerical_box:focus, .o_survey_session_manage .o_survey_question_text_box:focus,
.o_survey_session_manage .o_survey_question_date:focus,
.o_survey_session_manage .o_survey_question_datetime:focus,
.o_survey_session_manage .o_survey_question_numerical_box:focus, .o_survey_quick_access .o_survey_question_text_box:focus,
.o_survey_quick_access .o_survey_question_date:focus,
.o_survey_quick_access .o_survey_question_datetime:focus,
.o_survey_quick_access .o_survey_question_numerical_box:focus {
  box-shadow: none;
}

.o_survey_form div.bg-danger .o_survey_question_char_box,
.o_survey_form div.bg-danger .o_survey_question_date,
.o_survey_form div.bg-danger .o_survey_question_datetime,
.o_survey_form div.bg-danger .o_survey_question_numerical_box,
.o_survey_form div.bg-danger .o_survey_question_text_box, .o_survey_form div.bg-success .o_survey_question_char_box,
.o_survey_form div.bg-success .o_survey_question_date,
.o_survey_form div.bg-success .o_survey_question_datetime,
.o_survey_form div.bg-success .o_survey_question_numerical_box,
.o_survey_form div.bg-success .o_survey_question_text_box, .o_survey_form div.o_survey_question_skipped .o_survey_question_char_box,
.o_survey_form div.o_survey_question_skipped .o_survey_question_date,
.o_survey_form div.o_survey_question_skipped .o_survey_question_datetime,
.o_survey_form div.o_survey_question_skipped .o_survey_question_numerical_box,
.o_survey_form div.o_survey_question_skipped .o_survey_question_text_box, .o_survey_print div.bg-danger .o_survey_question_char_box,
.o_survey_print div.bg-danger .o_survey_question_date,
.o_survey_print div.bg-danger .o_survey_question_datetime,
.o_survey_print div.bg-danger .o_survey_question_numerical_box,
.o_survey_print div.bg-danger .o_survey_question_text_box, .o_survey_print div.bg-success .o_survey_question_char_box,
.o_survey_print div.bg-success .o_survey_question_date,
.o_survey_print div.bg-success .o_survey_question_datetime,
.o_survey_print div.bg-success .o_survey_question_numerical_box,
.o_survey_print div.bg-success .o_survey_question_text_box, .o_survey_print div.o_survey_question_skipped .o_survey_question_char_box,
.o_survey_print div.o_survey_question_skipped .o_survey_question_date,
.o_survey_print div.o_survey_question_skipped .o_survey_question_datetime,
.o_survey_print div.o_survey_question_skipped .o_survey_question_numerical_box,
.o_survey_print div.o_survey_question_skipped .o_survey_question_text_box, .o_survey_session_manage div.bg-danger .o_survey_question_char_box,
.o_survey_session_manage div.bg-danger .o_survey_question_date,
.o_survey_session_manage div.bg-danger .o_survey_question_datetime,
.o_survey_session_manage div.bg-danger .o_survey_question_numerical_box,
.o_survey_session_manage div.bg-danger .o_survey_question_text_box, .o_survey_session_manage div.bg-success .o_survey_question_char_box,
.o_survey_session_manage div.bg-success .o_survey_question_date,
.o_survey_session_manage div.bg-success .o_survey_question_datetime,
.o_survey_session_manage div.bg-success .o_survey_question_numerical_box,
.o_survey_session_manage div.bg-success .o_survey_question_text_box, .o_survey_session_manage div.o_survey_question_skipped .o_survey_question_char_box,
.o_survey_session_manage div.o_survey_question_skipped .o_survey_question_date,
.o_survey_session_manage div.o_survey_question_skipped .o_survey_question_datetime,
.o_survey_session_manage div.o_survey_question_skipped .o_survey_question_numerical_box,
.o_survey_session_manage div.o_survey_question_skipped .o_survey_question_text_box, .o_survey_quick_access div.bg-danger .o_survey_question_char_box,
.o_survey_quick_access div.bg-danger .o_survey_question_date,
.o_survey_quick_access div.bg-danger .o_survey_question_datetime,
.o_survey_quick_access div.bg-danger .o_survey_question_numerical_box,
.o_survey_quick_access div.bg-danger .o_survey_question_text_box, .o_survey_quick_access div.bg-success .o_survey_question_char_box,
.o_survey_quick_access div.bg-success .o_survey_question_date,
.o_survey_quick_access div.bg-success .o_survey_question_datetime,
.o_survey_quick_access div.bg-success .o_survey_question_numerical_box,
.o_survey_quick_access div.bg-success .o_survey_question_text_box, .o_survey_quick_access div.o_survey_question_skipped .o_survey_question_char_box,
.o_survey_quick_access div.o_survey_question_skipped .o_survey_question_date,
.o_survey_quick_access div.o_survey_question_skipped .o_survey_question_datetime,
.o_survey_quick_access div.o_survey_question_skipped .o_survey_question_numerical_box,
.o_survey_quick_access div.o_survey_question_skipped .o_survey_question_text_box {
  border: 0;
  color: #FFF !important;
  font-weight: 700;
  height: 2rem;
}

.o_survey_form .o_survey_form_date [data-toggle="datetimepicker"], .o_survey_print .o_survey_form_date [data-toggle="datetimepicker"], .o_survey_session_manage .o_survey_form_date [data-toggle="datetimepicker"], .o_survey_quick_access .o_survey_form_date [data-toggle="datetimepicker"] {
  right: 0;
  bottom: 5px;
  top: auto;
}

.o_survey_form .o_survey_choice_btn, .o_survey_print .o_survey_choice_btn, .o_survey_session_manage .o_survey_choice_btn, .o_survey_quick_access .o_survey_choice_btn {
  transition: background-color 0.3s ease;
  flex: 1 0 300px;
  color: #35979c;
}

.o_survey_form .o_survey_choice_btn span, .o_survey_print .o_survey_choice_btn span, .o_survey_session_manage .o_survey_choice_btn span, .o_survey_quick_access .o_survey_choice_btn span {
  line-height: 25px;
}

.o_survey_form .o_survey_choice_btn i, .o_survey_print .o_survey_choice_btn i, .o_survey_session_manage .o_survey_choice_btn i, .o_survey_quick_access .o_survey_choice_btn i {
  top: 0px;
  font-size: large;
}

.o_survey_form .o_survey_choice_btn i.fa-check-circle, .o_survey_print .o_survey_choice_btn i.fa-check-circle, .o_survey_session_manage .o_survey_choice_btn i.fa-check-circle, .o_survey_quick_access .o_survey_choice_btn i.fa-check-circle {
  display: none;
}

.o_survey_form .o_survey_choice_btn.o_survey_selected i, .o_survey_print .o_survey_choice_btn.o_survey_selected i, .o_survey_session_manage .o_survey_choice_btn.o_survey_selected i, .o_survey_quick_access .o_survey_choice_btn.o_survey_selected i {
  display: none;
}

.o_survey_form .o_survey_choice_btn.o_survey_selected i.fa-check-circle, .o_survey_print .o_survey_choice_btn.o_survey_selected i.fa-check-circle, .o_survey_session_manage .o_survey_choice_btn.o_survey_selected i.fa-check-circle, .o_survey_quick_access .o_survey_choice_btn.o_survey_selected i.fa-check-circle {
  display: inline;
}

.o_survey_form input::placeholder, .o_survey_form textarea::placeholder, .o_survey_print input::placeholder, .o_survey_print textarea::placeholder, .o_survey_session_manage input::placeholder, .o_survey_session_manage textarea::placeholder, .o_survey_quick_access input::placeholder, .o_survey_quick_access textarea::placeholder {
  font-weight: 300;
}

@media (min-width: 768px) {
  .o_survey_form .o_survey_page_per_question.o_survey_simple_choice.o_survey_minimized_display,
.o_survey_form .o_survey_page_per_question.o_survey_multiple_choice.o_survey_minimized_display,
.o_survey_form .o_survey_page_per_question.o_survey_numerical_box,
.o_survey_form .o_survey_page_per_question.o_survey_date,
.o_survey_form .o_survey_page_per_question.o_survey_datetime, .o_survey_print .o_survey_page_per_question.o_survey_simple_choice.o_survey_minimized_display,
.o_survey_print .o_survey_page_per_question.o_survey_multiple_choice.o_survey_minimized_display,
.o_survey_print .o_survey_page_per_question.o_survey_numerical_box,
.o_survey_print .o_survey_page_per_question.o_survey_date,
.o_survey_print .o_survey_page_per_question.o_survey_datetime, .o_survey_session_manage .o_survey_page_per_question.o_survey_simple_choice.o_survey_minimized_display,
.o_survey_session_manage .o_survey_page_per_question.o_survey_multiple_choice.o_survey_minimized_display,
.o_survey_session_manage .o_survey_page_per_question.o_survey_numerical_box,
.o_survey_session_manage .o_survey_page_per_question.o_survey_date,
.o_survey_session_manage .o_survey_page_per_question.o_survey_datetime, .o_survey_quick_access .o_survey_page_per_question.o_survey_simple_choice.o_survey_minimized_display,
.o_survey_quick_access .o_survey_page_per_question.o_survey_multiple_choice.o_survey_minimized_display,
.o_survey_quick_access .o_survey_page_per_question.o_survey_numerical_box,
.o_survey_quick_access .o_survey_page_per_question.o_survey_date,
.o_survey_quick_access .o_survey_page_per_question.o_survey_datetime {
    width: 50%;
    position: relative;
    left: 25%;
  }
}

.o_survey_form .o_survey_question_matrix td, .o_survey_print .o_survey_question_matrix td, .o_survey_session_manage .o_survey_question_matrix td, .o_survey_quick_access .o_survey_question_matrix td {
  min-width: 100px;
}

.o_survey_form .o_survey_question_matrix td i, .o_survey_print .o_survey_question_matrix td i, .o_survey_session_manage .o_survey_question_matrix td i, .o_survey_quick_access .o_survey_question_matrix td i {
  font-size: 22px;
  display: none;
}

.o_survey_form .o_survey_question_matrix td i.o_survey_matrix_empty_checkbox, .o_survey_print .o_survey_question_matrix td i.o_survey_matrix_empty_checkbox, .o_survey_session_manage .o_survey_question_matrix td i.o_survey_matrix_empty_checkbox, .o_survey_quick_access .o_survey_question_matrix td i.o_survey_matrix_empty_checkbox {
  display: inline;
}

.o_survey_form .o_survey_question_matrix td .o_survey_choice_key, .o_survey_print .o_survey_question_matrix td .o_survey_choice_key, .o_survey_session_manage .o_survey_question_matrix td .o_survey_choice_key, .o_survey_quick_access .o_survey_question_matrix td .o_survey_choice_key {
  left: 10px;
  right: auto;
  top: 12px;
}

.o_survey_form .o_survey_question_matrix td .o_survey_choice_key > span > span, .o_survey_print .o_survey_question_matrix td .o_survey_choice_key > span > span, .o_survey_session_manage .o_survey_question_matrix td .o_survey_choice_key > span > span, .o_survey_quick_access .o_survey_question_matrix td .o_survey_choice_key > span > span {
  top: 0px;
}

.o_survey_form .o_survey_question_matrix td.o_survey_selected i, .o_survey_print .o_survey_question_matrix td.o_survey_selected i, .o_survey_session_manage .o_survey_question_matrix td.o_survey_selected i, .o_survey_quick_access .o_survey_question_matrix td.o_survey_selected i {
  display: inline;
}

.o_survey_form .o_survey_question_matrix td.o_survey_selected i.o_survey_matrix_empty_checkbox, .o_survey_print .o_survey_question_matrix td.o_survey_selected i.o_survey_matrix_empty_checkbox, .o_survey_session_manage .o_survey_question_matrix td.o_survey_selected i.o_survey_matrix_empty_checkbox, .o_survey_quick_access .o_survey_question_matrix td.o_survey_selected i.o_survey_matrix_empty_checkbox {
  display: none;
}

.o_survey_form .o_survey_question_matrix thead th:first-child, .o_survey_print .o_survey_question_matrix thead th:first-child, .o_survey_session_manage .o_survey_question_matrix thead th:first-child, .o_survey_quick_access .o_survey_question_matrix thead th:first-child {
  border-top-left-radius: .25rem;
}

.o_survey_form .o_survey_question_matrix thead th:last-child, .o_survey_print .o_survey_question_matrix thead th:last-child, .o_survey_session_manage .o_survey_question_matrix thead th:last-child, .o_survey_quick_access .o_survey_question_matrix thead th:last-child {
  border-top-right-radius: .25rem;
}

.o_survey_form .o_survey_question_matrix tbody tr:last-child th, .o_survey_print .o_survey_question_matrix tbody tr:last-child th, .o_survey_session_manage .o_survey_question_matrix tbody tr:last-child th, .o_survey_quick_access .o_survey_question_matrix tbody tr:last-child th {
  border-bottom-left-radius: .25rem;
}

.o_survey_form .o_survey_question_matrix tbody tr:last-child td:last-child, .o_survey_print .o_survey_question_matrix tbody tr:last-child td:last-child, .o_survey_session_manage .o_survey_question_matrix tbody tr:last-child td:last-child, .o_survey_quick_access .o_survey_question_matrix tbody tr:last-child td:last-child {
  border-bottom-right-radius: .25rem;
}

.o_survey_quick_access .o_survey_error {
  min-height: 2rem;
}

.o_survey_quick_access #session_code {
  font-size: 4rem;
}

.o_survey_form .o_survey_question_matrix th, .o_survey_session_manage .o_survey_question_matrix th {
  background-color: #35979c;
}

.o_survey_form .o_survey_question_matrix td, .o_survey_session_manage .o_survey_question_matrix td {
  background-color: rgba(53, 151, 156, 0.2);
}

/**********************************************************
                    Form Specific Style
 **********************************************************/
.o_survey_form {
  min-height: 25rem;
}

.o_survey_form .o_survey_choice_btn {
  cursor: pointer;
  background-color: rgba(53, 151, 156, 0.1);
  box-shadow: #35979c 0px 0px 0px 1px;
}

.o_survey_form .o_survey_choice_btn.o_survey_selected {
  box-shadow: #35979c 0px 0px 0px 2px;
}

.o_survey_form .o_survey_choice_btn:hover {
  background-color: rgba(53, 151, 156, 0.3);
}

.o_survey_form .o_survey_choice_btn:hover .o_survey_choice_key span.o_survey_key {
  opacity: 1;
}

.o_survey_form .o_survey_choice_img img {
  max-width: 95%;
  max-height: 60vh;
  cursor: zoom-in;
}

.o_survey_form .o_survey_choice_img img:hover {
  box-sizing: border-box;
  box-shadow: 0 0 5px 2px grey;
}

.o_survey_form .o_survey_choice_key {
  width: 25px;
  height: 25px;
  border: 1px solid #35979c;
}

.o_survey_form .o_survey_choice_key span {
  font-size: smaller;
  top: -1px;
}

.o_survey_form .o_survey_choice_key span.o_survey_key {
  right: 21px;
  border: 1px solid #35979c;
  border-right: 0px;
  height: 25px;
  transition: opacity 0.4s ease;
  white-space: nowrap;
  opacity: 0;
}

.o_survey_form .o_survey_choice_key span.o_survey_key span {
  top: -2px;
}

.o_survey_form .o_survey_question_matrix td:hover {
  background-color: rgba(53, 151, 156, 0.5);
  cursor: pointer;
}

.o_survey_form .o_survey_question_matrix td:hover .o_survey_choice_key span.o_survey_key {
  opacity: 1;
}

/**********************************************************
                Survey Session Specific Style
 **********************************************************/
.o_survey_session_manage h1 {
  font-size: 3rem;
}

.o_survey_session_manage h2 {
  font-size: 2.5rem;
}

.o_survey_session_manage .o_survey_session_navigation {
  position: fixed;
  padding: 1rem;
  top: calc(50% - 0.5rem);
  cursor: pointer;
}

.o_survey_session_manage .o_survey_session_navigation.o_survey_session_navigation_next {
  right: 1rem;
}

.o_survey_session_manage .o_survey_session_navigation.o_survey_session_navigation_previous {
  left: 1rem;
}

.o_survey_session_manage .o_survey_manage_fontsize_14 {
  font-size: 1.4rem;
}

.o_survey_session_manage .o_survey_question_header {
  top: 1em;
}

.o_survey_session_manage .o_survey_question_header > div {
  width: 400px;
}

.o_survey_session_manage .o_survey_question_header .progress {
  height: 2rem;
  border-radius: 0.6rem;
  font-size: 1.2rem;
  background-color: #cfcfcf;
}

.o_survey_session_manage .o_survey_question_header .progress .progress-bar {
  width: 0%;
  transition: width 1s ease;
}

.o_survey_session_manage .o_survey_session_manage_container .o_survey_choice_key {
  display: none;
}

.o_survey_session_manage .o_survey_session_manage_container.pt-6 {
  padding-top: 5rem !important;
}

.o_survey_session_manage .o_survey_session_manage_container .o_survey_session_results {
  display: -webkit-box; display: -webkit-flex; display: flex;
}

.o_survey_session_manage .o_survey_session_manage_container .o_survey_session_results .mb-6 {
  margin-bottom: 6rem;
}

.o_survey_session_manage .o_survey_session_manage_container .o_survey_session_results .o_survey_session_text_answer .o_survey_session_text_answer_container {
  border: solid 1.6px;
  border-radius: 0.6rem;
  font-size: 1.4rem;
  width: 2rem;
  opacity: .1;
  transition: width .4s ease, opacity .4s ease;
  overflow: hidden;
}

.o_survey_session_manage .o_survey_session_manage_container .o_survey_session_results .o_survey_session_text_answer span {
  white-space: nowrap;
}

.o_survey_session_manage .o_survey_session_manage_container .o_survey_session_leaderboard {
  display: -webkit-box; display: -webkit-flex; display: flex;
}

.o_survey_session_manage .o_survey_session_manage_container .o_survey_session_leaderboard .o_survey_leaderboard_buttons {
  line-height: 4rem;
  font-variant: small-caps;
}

.o_survey_session_manage .o_survey_session_copy {
  cursor: pointer;
}

.o_survey_session_leaderboard {
  font-size: 1.4rem;
}

.o_survey_session_leaderboard .o_survey_session_leaderboard_container {
  height: calc(2.8rem * 15);
}

.o_survey_session_leaderboard .o_survey_session_leaderboard_item {
  line-height: 2.4rem;
  width: 100%;
  transition: top ease-in-out .3s;
}

.o_survey_session_leaderboard .o_survey_session_leaderboard_item .o_survey_session_leaderboard_score {
  width: 6.5rem;
  padding-top: .2rem;
  height: 2.8rem;
}

.o_survey_session_leaderboard .o_survey_session_leaderboard_item .o_survey_session_leaderboard_bar, .o_survey_session_leaderboard .o_survey_session_leaderboard_item .o_survey_session_leaderboard_bar_question {
  height: 2.8rem;
}

.o_survey_session_leaderboard .o_survey_session_leaderboard_item .o_survey_session_leaderboard_bar {
  min-width: 3rem;
  background-color: #007A77;
  z-index: 2;
}

.o_survey_session_leaderboard .o_survey_session_leaderboard_item .o_survey_session_leaderboard_bar_question_score {
  top: .2rem;
  right: .5rem;
  width: 20rem;
  z-index: 1;
}

.o_survey_session_leaderboard .o_survey_session_leaderboard_item .o_survey_session_leaderboard_name {
  padding-top: .2rem;
  width: 7.5rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/**********************************************************
                   Print Specific Style
 **********************************************************/
.o_survey_print .o_survey_choice_btn {
  background-color: #ADB5BD;
  border-color: transparent;
  cursor: default;
  color: white;
  font-weight: bold;
}

.o_survey_print .o_survey_choice_btn.bg-success, .o_survey_print .o_survey_choice_btn.bg-danger {
  opacity: 0.6;
}

.o_survey_print .o_survey_choice_btn.o_survey_selected {
  background-color: #6C757D;
  opacity: 1;
}

.o_survey_print .o_survey_choice_btn i.fa-circle-thin {
  display: none;
}

.o_survey_print .o_survey_question_matrix th {
  /* important needed to force override bg-primary set on th in the template */
  background-color: #6C757D !important;
}

.o_survey_print .o_survey_question_matrix td {
  background-color: #E9ECEF;
}

.o_survey_print .o_survey_question_matrix td:hover {
  cursor: default;
}

.o_survey_print .o_survey_question_matrix i.fa-check-square, .o_survey_print .o_survey_question_matrix i.fa-check-circle, .o_survey_print .o_survey_question_matrix i.o_survey_matrix_empty_checkbox {
  color: #6C757D;
}

.o_survey_print .o_survey_question_skipped {
  background-color: #d39e00;
}

.o_survey_print .o_survey_choice_question_skipped {
  color: #d39e00;
}

.o_survey_print .o_survey_choice_img img {
  cursor: default;
}

.o_survey_print .o_survey_choice_img img:hover {
  box-shadow: none;
}

/**********************************************************
    Zoomer Specific Style (SurveyImageZoomer widget)
    When the width is small (mobile), let space above and below
    to indicate that the user can close it by clicking out.
 **********************************************************/
.o_survey_img_zoom_modal {
  cursor: pointer;
}

.o_survey_img_zoom_modal .o_survey_img_zoom_dialog {
  background-color: rgba(0, 0, 0, 0.65);
}

@media (max-width: 575.98px) {
  .o_survey_img_zoom_modal .o_survey_img_zoom_dialog {
    height: 80% !important;
  }
}

.o_survey_img_zoom_modal .o_survey_img_zoom_dialog .o_survey_img_zoom_body {
  font-size: 1.5rem;
}

.o_survey_img_zoom_modal .o_survey_img_zoom_dialog .o_survey_img_zoom_body img {
  max-width: 90%;
  min-width: clamp(250px, 60%, 450px);
  max-height: 90%;
  object-fit: contain;
}

.o_survey_img_zoom_modal .o_survey_img_zoom_dialog .o_survey_img_zoom_body .o_survey_img_zoom_close_btn {
  right: 12px;
  top: 12px;
  z-index: 1;
}

.o_survey_img_zoom_modal .o_survey_img_zoom_dialog .o_survey_img_zoom_body .o_survey_img_zoom_controls_wrapper {
  bottom: 5%;
}

.o_survey_img_zoom_modal .o_survey_img_zoom_dialog .o_survey_img_zoom_body .o_survey_img_zoom_controls_wrapper .o_survey_img_zoom_in_btn, .o_survey_img_zoom_modal .o_survey_img_zoom_dialog .o_survey_img_zoom_body .o_survey_img_zoom_controls_wrapper .o_survey_img_zoom_out_btn {
  background-color: rgba(0, 0, 0, 0.65);
}

.o_survey_img_zoom_modal .o_survey_img_zoom_dialog .o_survey_img_zoom_body .o_survey_img_zoom_controls_wrapper .o_survey_img_zoom_in_btn:hover .fa, .o_survey_img_zoom_modal .o_survey_img_zoom_dialog .o_survey_img_zoom_body .o_survey_img_zoom_controls_wrapper .o_survey_img_zoom_out_btn:hover .fa {
  color: grey;
}

.modal-open .o_survey_background {
  overflow: auto !important;
}



/* /survey/static/src/scss/survey_templates_results.scss */

@media print {
  .chartjs-size-monitor {
    display: none;
  }
  .chartjs-render-monitor {
    width: 100% !important;
    height: 100% !important;
  }
  .tab-content > .tab-pane {
    display: block;
  }
  html {
    height: unset;
  }
}

.o_survey_results_topbar {
  border: 1px solid rgba(0, 0, 0, 0.125);
}

.o_survey_results_topbar .nav-item.dropdown a {
  min-width: 13em;
}

.o_survey_results_topbar .o_survey_results_topbar_dropdown_filters .dropdown-toggle {
  text-align: left;
}

.o_survey_results_topbar .o_survey_results_topbar_dropdown_filters .dropdown-toggle:hover, .o_survey_results_topbar .o_survey_results_topbar_dropdown_filters .dropdown-toggle:focus {
  text-decoration: none;
}

.o_survey_results_topbar .o_survey_results_topbar_dropdown_filters .dropdown-toggle:after {
  float: right;
  margin-top: .5em;
}

.o_survey_results_topbar .o_survey_results_topbar_dropdown_filters .dropdown-toggle .fa {
  margin-right: .4em;
}

.o_survey_results_topbar .o_survey_results_topbar_dropdown_filters .dropdown-menu {
  margin-top: 0.5rem;
  min-width: 12rem;
  max-height: 250px;
  overflow-y: auto;
}

.o_survey_results_topbar .o_survey_results_topbar_dropdown_filters .dropdown-item.active .badge {
  background-color: #FFFFFF;
  color: #35979c;
}

.o_survey_results_topbar .o_survey_results_topbar_answer_filters .btn.filter-remove-answer {
  border-color: #DEE2E6;
  background-color: transparent;
  white-space: normal;
  text-align: left;
}

.o_survey_results_topbar .o_survey_results_topbar_answer_filters .btn.filter-remove-answer i.fa-times {
  cursor: pointer;
}

.o_survey_results_topbar .o_survey_results_topbar_clear_filters {
  cursor: pointer;
}

.o_survey_results_topbar .o_survey_results_topbar_clear_filters:hover {
  text-decoration: underline;
}

.o_survey_results_question .o_survey_results_question_pill .only_right_radius {
  border-radius: 0 2em 2em 0;
}

.o_survey_results_question .o_survey_results_question_pill .only_left_radius {
  border-radius: 2em 0 0 2em;
}

.o_survey_results_question .o_survey_answer i {
  padding: 3px;
  cursor: pointer;
}

.o_survey_results_question .o_survey_answer i.o_survey_answer_matrix_whitespace {
  padding-right: 18px;
  cursor: default;
}

.o_survey_results_question .nav-tabs .nav-link.active {
  background-color: transparent;
  border-color: #DEE2E6;
  font-weight: bold;
}



/* /website_slides_survey/static/src/scss/website_slides_survey_result.scss */

/*
When website is installed, modal overlaps the navbar. And to fix that,
'top' css property has been utilized. As a side-effect, it shows white
stripe in some survey related pages (due to hidden navbar).

So we simply set the 'top' to 0 to for avoiding it for those pages. Thankfully,
for such pages, there's a separate asset bundle where we include this file, and
so it won't affect the rest of the website pages.
*/
#wrapwrap .modal {
  top: 0;
}
//*# sourceMappingURL=/web/assets/13834-70e2168/1/survey.survey_assets.css.map */