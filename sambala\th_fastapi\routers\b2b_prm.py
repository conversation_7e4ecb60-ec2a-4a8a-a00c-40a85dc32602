from typing import Annotated
from fastapi import APIRouter, Depends, HTTPException
from ..dependencies import authenticated_fastapi_endpoint
from odoo.addons.fastapi.models.fastapi_endpoint import FastapiEndpoint as ThFastapi
import time

router = APIRouter(tags=["PRM"])

@router.post("/api/formio/prmleads")
def create_prm_lead(
    lead_data: dict,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    start = time.perf_counter()
    try:
        if not fastapi:
            raise HTTPException(status_code=401, detail="Không có quyền truy cập")

        # Khởi tạo các giá trị mặc định
        th_partner_referred_id = False
        th_formio_pro_id = False
        th_ownership = fastapi.env['th.ownership.unit'].search([('th_code', '=', lead_data.get('context', {}).get('th_ownership_code'))], limit=1)
        check_module = fastapi.env['ir.config_parameter'].sudo().get_param('th_check_partner_module')

        # X<PERSON> lý người giới thiệu
        if lead_data.get('th_affiliate_code'):
            th_partner_referred_id = fastapi.env['res.partner'].search(
                [('th_affiliate_code', '=', lead_data.get('th_affiliate_code'))], limit=1)

        # Tìm kiếm partner
        domain = []
        if lead_data.get('th_phone'):
            domain = ['|', ('phone', '=', lead_data.get('th_phone')), ('th_phone2', '=', lead_data.get('th_phone'))]
        if lead_data.get('th_email'):
            domain = ['|',] + domain + [('email', '=', lead_data.get('th_email'))] if domain else [('email', '=', lead_data.get('th_email'))]
        if check_module:
            domain.append(('th_module_ids', 'in', fastapi.env.ref('th_setup_parameters.th_prm_module').ids))

        partner = False
        if domain:
            partner = fastapi.env['res.partner'].search(domain, limit=1)
            if partner:
                 # kiểm tra check trùng
                if lead_data.get('domain'):
                    exist_lead = fastapi.env['prm.lead'].sudo().search(lead_data.get('domain'))
                if exist_lead:
                    vals = {
                        'exist_lead_ids': exist_lead.ids
                    }
                    fastapi.env['prm.lead'].th_send_mail_duplicate(vals)
                    return {
                            'exist_lead': exist_lead.ids,
                            'duplicate': True
                        }
                # Cập nhật thông tin phone cho partner nếu cần
                if partner.phone and not partner.th_phone2 and lead_data.get('th_phone') and partner.phone != lead_data.get('th_phone'):
                    partner.write({'th_phone2': lead_data.get('th_phone')})
                elif not partner.phone and lead_data.get('th_phone'):
                    partner.write({'phone': lead_data.get('th_phone')})
                elif partner.th_phone2 and partner.phone and lead_data.get('th_phone') and lead_data.get('th_phone') not in [partner.th_phone2, partner.phone]:
                    partner.write({
                        'comment': partner.comment + '\nSố điện thoại mới: ' + lead_data.get('th_phone') if partner.comment 
                        else 'Số điện thoại mới: ' + lead_data.get('th_phone')
                    })

        # Tạo partner mới nếu chưa tồn tại
        if not partner:
            partner = fastapi.env['res.partner'].create({
                'name': lead_data.get('th_customer') or lead_data.get('th_phone'),
                'phone': lead_data.get('th_phone'),
                'email': lead_data.get('th_email'),
                'th_module_ids': [(4, fastapi.env.ref('th_setup_parameters.th_prm_module').id)],
            })

        # Tạo cơ hội mới
        new_opportunity = {
            'th_partner_id': partner.id,
            'th_ownership_unit_id': th_ownership.id if th_ownership else False,
            'th_partner_reference_id': th_partner_referred_id.id if th_partner_referred_id else False,
            'th_lead_aff_id': lead_data.get('id'),
            'th_description': lead_data.get('th_description'),
            'th_user_id': False,
        }

        # Xử lý form configuration
        if lead_data.get('context').get('th_form_id') and lead_data.get('context').get('aff_prm_lead'):
            th_formio_pro_id = fastapi.env['th.formio.builder.field.aff.default'].search([
                ('th_uuid', '=', lead_data.get('context').get('th_form_id'))
            ])
            if th_formio_pro_id:
                new_opportunity.update({
                    'th_partner_group_ids': [[6, 0, th_formio_pro_id.th_partner_group_id.ids]] if th_formio_pro_id.th_partner_group_id else False,
                    'th_partner_source_id': th_formio_pro_id.th_partner_source_id.id if th_formio_pro_id.th_partner_source_id else False,
                    'th_call_status': th_formio_pro_id.th_status_category_id.id if th_formio_pro_id.th_status_category_id else False,
                    'th_user_id': th_formio_pro_id.action_assign_leads_prm(),
                })
        cooperation_text = "Chương trình hợp tác: " + lead_data.get('th_cooperation_program')
        if new_opportunity.get('th_description'):
            new_opportunity.update({
                'th_description': f"{new_opportunity.get('th_description')}\n{cooperation_text}",
            })
        else:
            new_opportunity.update({
                'th_description': cooperation_text,
            })
        
       
        
        # Tạo PRM Lead
        prm_lead = fastapi.env['prm.lead'].sudo().create(new_opportunity)

        # Kiểm tra trùng lặp
        result = {
            'prm_id': prm_lead.id,
            'duplicate': False
        }

        if lead_data.get('id') and prm_lead:
            exist_lead = fastapi.env['prm.lead'].sudo().search([
                ('id', '!=', prm_lead.id),
                ('th_partner_id', '=', prm_lead.th_partner_id.id),
                ('th_ownership_unit_id', '=', prm_lead.th_ownership_unit_id.id),
                ('th_is_a_duplicate_opportunity', '=', False)
            ], limit=1)

            if exist_lead:
                result['duplicate'] = True
                result['exist_lead_id'] = exist_lead.id

        result['th_processing_time'] = round(time.perf_counter() - start, 4) * 1000
        return result

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
